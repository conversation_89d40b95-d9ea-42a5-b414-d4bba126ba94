# GitHub Actions Workflow for launching UI tests on Linux, Windows, and Mac in the following steps:
# - Prepare and launch IDE with your plugin and robot-server plugin, which is needed to interact with the UI.
# - Wait for IDE to start.
# - Run UI tests with a separate Gradle task.
#
# Please check https://github.com/JetBrains/intellij-ui-test-robot for information about UI tests with IntelliJ Platform.
#
# Workflow is triggered manually.

name: Run UI Tests
on:
  workflow_dispatch

jobs:

  testUI:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            runIde: |
              export DISPLAY=:99.0
              Xvfb -ac :99 -screen 0 1920x1080x16 &
              gradle runIdeForUiTests &
          - os: windows-latest
            runIde: start gradlew.bat runIdeForUiTests
          - os: macos-latest
            runIde: ./gradlew runIdeForUiTests &

    steps:

      # Check out the current repository
      - name: Fetch Sources
        uses: actions/checkout@v4

      # Set up Java environment for the next steps
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 21

      # Setup Gradle
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      # Run IDEA prepared for UI testing
      - name: Run IDE
        run: ${{ matrix.runIde }}

      # Wait for IDEA to be started
      - name: Health Check
        uses: jtalk/url-health-check-action@v4
        with:
          url: http://127.0.0.1:8082
          max-attempts: 15
          retry-delay: 30s

      # Run tests
      - name: Tests
        run: ./gradlew test
