package club.bigtian.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class RolePersonExport {

    @Excel(name = "角色编码", width = 30)
    private String roleCode;

    @Excel(name = "人员编码", width = 30)
    private String personCode;

    @Excel(name = "更新策略", width = 30)
    private String updateStrategy;

    @Excel(name = "租户", width = 30)
    private String tenantId;

}
