package club.bigtian.entity;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static club.bigtian.handler.ExcelDicHandler.LANG;
import static club.bigtian.handler.ExcelDicHandler.TYPE;
@Data

public class InternationalExport {


    @Excel(name = "编码", width = 50)
    private String code;

    @Excel(name = "名称", width = 50)
    private String name;

    @Excel(name = "语言", width = 20, addressList = true, dict = LANG)
    private String lang;

    @Excel(name = "类型", width = 20, addressList = true, dict = TYPE)
    private String type;

    @Excel(name = "更新策略")
    private String updateStrategy;

    @Excel(name = "租户")
    private String tenantId;





    public static List<InternationalExport> parseFromClipboard(String clipboardText) {
        List<InternationalExport> results = new ArrayList<>();

        // 按行分割
        String[] lines = clipboardText.split("\n");
        if (lines.length < 2) {  // 至少需要标题行和数据行
            return results;
        }

        // 解析标题行，使用制表符分割
        String[] headers = lines[0].trim().split("\t");

        // 创建header到field的映射
        Map<String, Field> headerFieldMap = createHeaderFieldMap();

        // 解析数据行
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.isEmpty()) continue;

            String[] values = line.split("\t");
            InternationalExport config = new InternationalExport();

            for (int j = 0; j < Math.min(headers.length, values.length); j++) {
                String header = headers[j].trim();
                String value = values[j].trim();

                // 通过反射设置属性值
                setFieldValue(config, header, value, headerFieldMap);
            }

            results.add(config);
        }

        return results;
    }

    private static Map<String, Field> createHeaderFieldMap() {
        Map<String, Field> map = new HashMap<>();

        // 获取所有带有@Excel注解的字段
        for (Field field : InternationalExport.class.getDeclaredFields()) {
            Excel excelAnnotation = field.getAnnotation(Excel.class);
            if (excelAnnotation != null) {
                map.put(excelAnnotation.name(), field);
            }
        }

        return map;
    }

    private static void setFieldValue(InternationalExport config, String header, String value, Map<String, Field> headerFieldMap) {
        try {
            Field field = headerFieldMap.get(header);
            if (field != null) {
                field.setAccessible(true);
                field.set(config, value);
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("设置字段值失败: " + header, e);
        }
    }
}
