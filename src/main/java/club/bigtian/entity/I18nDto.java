package club.bigtian.entity;


/**
 * <AUTHOR>
 */
public class I18nDto {
    /**
     * 国际化常量类名
     * CommonI18nConstant
     */
    private String code;
    /**
     * I18nUtils.getTitle(国际化常量类.常量名)
     * I18nUtils.getTitle(CommonI18nConstant.OPERATION_SUCCESS)
     */
    private String utilValue;

    /**
     * 国际化常量类.常量名
     * CommonI18nConstant.OPERATION_SUCCESS
     */
    private String value;
    /**
     * 国际化常量类的全限定名
     */
    private String qualifiedName;
    /**
     * 标题
     */
    private String title;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getQualifiedName() {
        return qualifiedName;
    }

    public void setQualifiedName(String qualifiedName) {
        this.qualifiedName = qualifiedName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUtilValue() {
        return utilValue;
    }

    public void setUtilValue(String utilValue) {
        this.utilValue = utilValue;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
