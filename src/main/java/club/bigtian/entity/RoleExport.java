package club.bigtian.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class RoleExport {
    //     角色编码	角色名称	角色类型	排序	备注	更新策略	租户	相关人员
    @Excel(name = "角色编码", width = 30)
    private String roleCode;

    @Excel(name = "角色名称", width = 30)
    private String roleName;

    @Excel(name = "角色类型", width = 30)
    private String roleType;

    @Excel(name = "排序", width = 30)
    private String sort;

    @Excel(name = "备注", width = 30)
    private String remark;

    @Excel(name = "更新策略", width = 30)
    private String updateStrategy;

    @Excel(name = "租户", width = 30)
    private String tenant;

    @Excel(name = "相关人员", width = 30)
    private String relatedPerson;
}
