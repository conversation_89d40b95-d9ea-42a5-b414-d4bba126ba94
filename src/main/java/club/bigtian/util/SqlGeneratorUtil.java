package club.bigtian.util;

import com.intellij.openapi.diagnostic.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Utility class for generating SQL statements from Excel data
 */
public class SqlGeneratorUtil {
    private static final Logger LOG = Logger.getInstance(SqlGeneratorUtil.class);

    /**
     * Generate SQL statements based on the provided data and mapping
     *
     * @param sqlType SQL statement type (INSERT, UPDATE, DELETE)
     * @param tableName Database table name
     * @param excelHeaders Excel file headers
     * @param excelData Excel data rows
     * @param columnMappings Mapping between Excel headers and database columns
     * @param primaryKeys List of primary key columns (for UPDATE/DELETE operations)
     * @param batchMode If true, generate SQL in batch mode, otherwise each statement separate
     * @param templateOnly If true, generate only one template example (for INSERT)
     * @return Generated SQL statements
     */
    public static String generateSql(String sqlType, String tableName, List<String> excelHeaders, 
                                    List<List<String>> excelData, Map<String, String> columnMappings,
                                    List<String> primaryKeys, boolean batchMode, boolean templateOnly) {
        StringBuilder sqlBuilder = new StringBuilder();
        
        if (sqlType == null || columnMappings == null || columnMappings.isEmpty()) {
            return "";
        }
        
        // Check if we have data (only required for non-template mode)
        if (!templateOnly && (excelData == null || excelData.isEmpty())) {
            return "";
        }
        
        switch (sqlType) {
            case "INSERT":
                generateInsertSql(sqlBuilder, tableName, excelHeaders, excelData, columnMappings, batchMode, templateOnly);
                break;
            case "UPDATE":
                generateUpdateSql(sqlBuilder, tableName, excelHeaders, excelData, columnMappings, primaryKeys, batchMode);
                break;
            case "DELETE":
                generateDeleteSql(sqlBuilder, tableName, excelHeaders, excelData, columnMappings, primaryKeys, batchMode);
                break;
            default:
                LOG.warn("Unsupported SQL type: " + sqlType);
                return "";
        }
        
        return sqlBuilder.toString();
    }
    
    /**
     * Legacy method for backward compatibility
     */
    public static String generateSql(String sqlType, String tableName, List<String> excelHeaders, 
                                    List<List<String>> excelData, Map<String, String> columnMappings,
                                    List<String> primaryKeys, boolean batchMode) {
        return generateSql(sqlType, tableName, excelHeaders, excelData, columnMappings, primaryKeys, batchMode, false);
    }
    
    /**
     * Legacy method for backward compatibility
     */
    public static String generateSql(String sqlType, String tableName, List<String> excelHeaders, 
                                    List<List<String>> excelData, Map<String, String> columnMappings) {
        return generateSql(sqlType, tableName, excelHeaders, excelData, columnMappings, List.of(), true, false);
    }
    
    /**
     * Generate INSERT SQL statements
     */
    private static void generateInsertSql(StringBuilder sqlBuilder, String tableName, List<String> excelHeaders, 
                                         List<List<String>> excelData, Map<String, String> columnMappings,
                                         boolean batchMode, boolean templateOnly) {
        // Get mapped DB columns - filter out null and empty values
        List<String> dbColumns = new ArrayList<>();
        
        for (String header : excelHeaders) {
            if (columnMappings.containsKey(header)) {
                String dbColumn = columnMappings.get(header);
                if (dbColumn != null && !dbColumn.isEmpty()) {
                    dbColumns.add(dbColumn);
                }
            }
        }
        
        // Generate column list string
        String columnList = String.join(", ", dbColumns);
        
        if (templateOnly) {
            // Generate just a template INSERT statement
            sqlBuilder.append("INSERT INTO ").append(tableName).append(" (").append(columnList).append(") VALUES (");
            
            boolean firstValue = true;
            
            // Process all headers including manual ones
            for (String header : excelHeaders) {
                // Skip unmapped or empty columns
                if (!columnMappings.containsKey(header) || 
                    columnMappings.get(header) == null || 
                    columnMappings.get(header).isEmpty()) {
                    continue;
                }
                
                // Add comma if not the first value
                if (!firstValue) {
                    sqlBuilder.append(", ");
                } else {
                    firstValue = false;
                }
                
                // For template, use column name as placeholder
                String columnName = columnMappings.get(header);
                sqlBuilder.append("'").append(columnName).append("'");
            }
            
            sqlBuilder.append(");");
            return;
        }
        
        if (batchMode) {
            // Generate batch INSERT statement
            sqlBuilder.append("INSERT INTO ").append(tableName).append(" (").append(columnList).append(") VALUES ");
            boolean firstRow = true;
            
            for (List<String> row : excelData) {
                if (!firstRow) {
                    sqlBuilder.append(",\n");
                } else {
                    firstRow = false;
                }
                
                sqlBuilder.append("(");
                boolean firstValue = true;
                
                for (int i = 0; i < excelHeaders.size(); i++) {
                    String header = excelHeaders.get(i);
                    
                    // Skip unmapped or empty columns
                    if (!columnMappings.containsKey(header) || 
                        columnMappings.get(header) == null || 
                        columnMappings.get(header).isEmpty()) {
                        continue;
                    }
                    
                    String value = (i < row.size()) ? row.get(i) : "";
                    
                    // Add comma if not the first value
                    if (!firstValue) {
                        sqlBuilder.append(", ");
                    } else {
                        firstValue = false;
                    }
                    
                    // Add value with quotes
                    sqlBuilder.append("'").append(escapeValue(value)).append("'");
                }
                
                sqlBuilder.append(")");
            }
            
            sqlBuilder.append(";\n");
        } else {
            // Generate individual INSERT statements
            for (List<String> row : excelData) {
                sqlBuilder.append("INSERT INTO ").append(tableName).append(" (").append(columnList).append(") VALUES (");
                
                boolean firstValue = true;
                for (int i = 0; i < excelHeaders.size(); i++) {
                    String header = excelHeaders.get(i);
                    
                    // Skip unmapped or empty columns
                    if (!columnMappings.containsKey(header) || 
                        columnMappings.get(header) == null || 
                        columnMappings.get(header).isEmpty()) {
                        continue;
                    }
                    
                    String value = (i < row.size()) ? row.get(i) : "";
                    
                    // Add comma if not the first value
                    if (!firstValue) {
                        sqlBuilder.append(", ");
                    } else {
                        firstValue = false;
                    }
                    
                    // Add value with quotes
                    sqlBuilder.append("'").append(escapeValue(value)).append("'");
                }
                
                sqlBuilder.append(");\n");
            }
        }
    }
    
    /**
     * Generate UPDATE SQL statements
     */
    private static void generateUpdateSql(StringBuilder sqlBuilder, String tableName, List<String> excelHeaders, 
                                         List<List<String>> excelData, Map<String, String> columnMappings,
                                         List<String> primaryKeys, boolean batchMode) {
        // If no primary keys provided, use the first mapped column
        if (primaryKeys == null || primaryKeys.isEmpty()) {
            String firstHeader = excelHeaders.get(0);
            String firstColumn = columnMappings.get(firstHeader);
            if (firstColumn != null && !firstColumn.isEmpty()) {
                primaryKeys = List.of(firstColumn);
            } else {
                sqlBuilder.append("-- Error: No primary key specified for UPDATE statement");
                return;
            }
        }
        
        // Find the indexes of primary key columns in Excel headers
        List<Integer> pkIndexes = new ArrayList<>();
        for (String pk : primaryKeys) {
            for (int i = 0; i < excelHeaders.size(); i++) {
                String header = excelHeaders.get(i);
                if (columnMappings.containsKey(header) && pk.equals(columnMappings.get(header))) {
                    pkIndexes.add(i);
                    break;
                }
            }
        }
        
        if (pkIndexes.isEmpty()) {
            sqlBuilder.append("-- Error: Primary key columns not found in Excel data");
            return;
        }
        
        // Generate SQL for each row
        for (List<String> row : excelData) {
            sqlBuilder.append("UPDATE ").append(tableName).append(" SET ");
            
            boolean firstColumn = true;
            
            // Generate SET clauses
            for (int i = 0; i < excelHeaders.size(); i++) {
                String header = excelHeaders.get(i);
                
                // Skip unmapped columns and primary key columns
                if (!columnMappings.containsKey(header) || 
                    columnMappings.get(header) == null ||
                    columnMappings.get(header).isEmpty() ||
                    primaryKeys.contains(columnMappings.get(header))) {
                    continue;
                }
                
                String dbColumn = columnMappings.get(header);
                String value = (i < row.size()) ? row.get(i) : "";
                
                // Add comma if not the first column
                if (!firstColumn) {
                    sqlBuilder.append(", ");
                } else {
                    firstColumn = false;
                }
                
                // Add column = value
                sqlBuilder.append(dbColumn).append(" = '").append(escapeValue(value)).append("'");
            }
            
            // Add WHERE clause with primary keys
            sqlBuilder.append(" WHERE ");
            for (int j = 0; j < pkIndexes.size(); j++) {
                int pkIndex = pkIndexes.get(j);
                String pkColumn = primaryKeys.get(j);
                String pkValue = (pkIndex < row.size()) ? row.get(pkIndex) : "";
                
                if (j > 0) {
                    sqlBuilder.append(" AND ");
                }
                
                sqlBuilder.append(pkColumn).append(" = '").append(escapeValue(pkValue)).append("'");
            }
            
            sqlBuilder.append(";\n");
        }
    }
    
    /**
     * Generate DELETE SQL statements
     */
    private static void generateDeleteSql(StringBuilder sqlBuilder, String tableName, List<String> excelHeaders, 
                                         List<List<String>> excelData, Map<String, String> columnMappings,
                                         List<String> primaryKeys, boolean batchMode) {
        // If no primary keys provided, use the first mapped column
        if (primaryKeys == null || primaryKeys.isEmpty()) {
            String firstHeader = excelHeaders.get(0);
            String firstColumn = columnMappings.get(firstHeader);
            if (firstColumn != null && !firstColumn.isEmpty()) {
                primaryKeys = List.of(firstColumn);
            } else {
                sqlBuilder.append("-- Error: No primary key specified for DELETE statement");
                return;
            }
        }
        
        // Find the indexes of primary key columns in Excel headers
        List<Integer> pkIndexes = new ArrayList<>();
        for (String pk : primaryKeys) {
            for (int i = 0; i < excelHeaders.size(); i++) {
                String header = excelHeaders.get(i);
                if (columnMappings.containsKey(header) && pk.equals(columnMappings.get(header))) {
                    pkIndexes.add(i);
                    break;
                }
            }
        }
        
        if (pkIndexes.isEmpty()) {
            sqlBuilder.append("-- Error: Primary key columns not found in Excel data");
            return;
        }
        
        // Generate SQL for each row
        for (List<String> row : excelData) {
            sqlBuilder.append("DELETE FROM ").append(tableName).append(" WHERE ");
            
            for (int j = 0; j < pkIndexes.size(); j++) {
                int pkIndex = pkIndexes.get(j);
                String pkColumn = primaryKeys.get(j);
                String pkValue = (pkIndex < row.size()) ? row.get(pkIndex) : "";
                
                if (j > 0) {
                    sqlBuilder.append(" AND ");
                }
                
                sqlBuilder.append(pkColumn).append(" = '").append(escapeValue(pkValue)).append("'");
            }
            
            sqlBuilder.append(";\n");
        }
    }
    
    /**
     * Escape special characters in SQL values
     */
    private static String escapeValue(String value) {
        if (value == null) {
            return "";
        }
        
        // Replace single quotes with two single quotes (SQL standard)
        return value.replace("'", "''");
    }
} 