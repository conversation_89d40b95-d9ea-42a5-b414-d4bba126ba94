package club.bigtian.util;

import com.intellij.ide.actions.RevealFileAction;
import com.intellij.notification.Notification;
import com.intellij.notification.NotificationAction;
import com.intellij.notification.NotificationGroup;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.SystemInfo;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;

public class FileNavigator {
    // 动态创建通知组，无需在 plugin.xml 中注册
    private static final NotificationGroup NOTIFICATION_GROUP =
            NotificationGroup.balloonGroup("FileNavigator.Notifications");

    /**
     * 显示一个包含文件链接的通知
     * @param project 当前项目
     * @param absolutePath 文件的绝对路径，如 "/User/xxx/a.zip"
     */
    public static void showFileNotification(Project project, String absolutePath,String title,String content,String actionText) {
        // 获取文件名
        String fileName = new File(absolutePath).getName();

        // 创建通知
        Notification notification = NOTIFICATION_GROUP.createNotification(
                title,
                content,
                NotificationType.INFORMATION);

        // 添加打开文件的操作按钮
        notification.addAction(new NotificationAction(actionText) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                showFileInExplorer(absolutePath);
                notification.expire();
            }
        });

        // 显示通知
        notification.notify(project);
    }

    public static void showFileInExplorer(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            // 首选方法：使用IntelliJ的RevealFileAction
            RevealFileAction.openDirectory(file);

            // 如果需要不仅打开文件夹，还要选中文件，可以使用以下方法
            // (Windows特有功能，其他系统下只打开文件夹)
            if (SystemInfo.isWindows) {
                try {
                    Runtime.getRuntime().exec(new String[]{"explorer.exe", "/select,", file.getAbsolutePath()});
                } catch (IOException e) {
                    // 回退到只打开文件夹
                    RevealFileAction.openDirectory(new File(file.getParent()));
                }
            }
        }
    }
}