package club.bigtian.util;

import com.intellij.ide.highlighter.JavaFileType;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.actionSystem.LangDataKeys;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.Project;
import com.intellij.psi.*;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.psi.search.PsiShortNamesCache;
import com.intellij.util.IncorrectOperationException;

import java.util.ArrayList;
import java.util.List;

/**
 * class工具类
 */
public class ClassUtils {

    /**
     * 创建class
     *
     * @param project     项目
     * @param className   类名
     * @param code        类代码
     * @param actionEvent 事件
     */
    public static void createClass(String className, String code, AnActionEvent actionEvent) {
        Project project = actionEvent.getProject();
        PsiFileFactory psiFileFactory = PsiFileFactory.getInstance(project);
        DataContext dataContext = actionEvent.getDataContext();
        PsiDirectory psiDirectory = (PsiDirectory) LangDataKeys.PSI_ELEMENT.getData(dataContext);
        String fileName = className + ".java";
        PsiFile newFile = psiFileFactory.createFileFromText(fileName, JavaFileType.INSTANCE, code);
        FileDocumentManager.getInstance().saveDocument(newFile.getViewProvider().getDocument());
        // 将新文件添加到选中的目录
        try {
            psiDirectory.add(newFile);
        } catch (IncorrectOperationException e) {
            e.printStackTrace();
            NotificationUtils.notifyError("文件已存在", "创建失败", project);
        }
    }


    /**
     * 得到包名
     *
     * @param e e
     * @return {@code String}
     */
    public static String getPackage(AnActionEvent e) {
        String packageName = "";
        PsiElement psiElement = e.getData(CommonDataKeys.PSI_ELEMENT);

        if (psiElement instanceof PsiDirectory) {
            PsiDirectory psiDirectory = (PsiDirectory) psiElement;
            PsiPackage psiPackage = JavaDirectoryService.getInstance().getPackage(psiDirectory);
            if (psiPackage != null) {
                packageName = psiPackage.getQualifiedName();
            }
        }
        return null;
    }


    /**
     * 搜索类
     *
     * @param project   项目
     * @param className 类名
     * @return {@code List<PsiClass>}
     */
    public static List<PsiClass> searchClasses(Project project, String className) {
        List<PsiClass> resultList = new ArrayList<>();

        GlobalSearchScope scope = GlobalSearchScope.allScope(project);
        PsiShortNamesCache namesCache = PsiShortNamesCache.getInstance(project);

        PsiClass[] classes = namesCache.getClassesByName(className, scope);
        for (PsiClass psiClass : classes) {
            resultList.add(psiClass);
        }

        return resultList;
    }

    public static PsiClass searchClasse(String className, Project project) {
        JavaPsiFacade psiFacade = JavaPsiFacade.getInstance(project);
        return psiFacade.findClass(className, GlobalSearchScope.allScope(psiFacade.getProject()));
    }
}
