package club.bigtian.util;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.intellij.openapi.diagnostic.Logger;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Utility class for Excel file operations
 */
public class ExcelUtil {
    private static final Logger LOG = Logger.getInstance(ExcelUtil.class);

    /**
     * Read Excel file and return data as list of row data
     *
     * @param filePath Path to Excel file
     * @return List of rows, where each row is a list of cell values as strings
     * @throws Exception If file cannot be read or parsed
     */
    public static List<List<String>> readExcel(String filePath) throws Exception {
        File excelFile = new File(filePath);
        if (!excelFile.exists()) {
            throw new Exception("Excel file does not exist: " + filePath);
        }

        List<List<String>> result = new ArrayList<>();
        
        try {
            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(FileUtil.file(filePath));
            
            // Get all sheets
            List<List<Object>> sheetData = reader.read();
            
            // Process each row
            for (List<Object> row : sheetData) {
                List<String> rowData = new ArrayList<>();
                
                // Convert cell values to string
                for (Object cell : row) {
                    rowData.add(cell != null ? cell.toString() : "");
                }
                
                result.add(rowData);
            }
            
            return result;
        } catch (Exception e) {
            LOG.error("Failed to read Excel file: " + filePath, e);
            throw e;
        }
    }

    /**
     * Get sheet names from Excel file
     *
     * @param filePath Path to Excel file
     * @return List of sheet names
     * @throws Exception If file cannot be read
     */
    public static List<String> getSheetNames(String filePath) throws Exception {
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(FileUtil.file(filePath));
        return reader.getSheetNames();
    }
} 