package club.bigtian.util;

import cn.hutool.core.util.StrUtil;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.actionSystem.LangDataKeys;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDirectory;

public class EditorUtils {
    /**
     * 打开当前生成的文件在编辑器
     *
     * @param className   类名
     * @param actionEvent 事件
     */
    public static void toEditorFile(String className, AnActionEvent actionEvent) {
        if (StrUtil.isEmpty(className)) {
            return;
        }
        Project project = actionEvent.getProject();
        DataContext dataContext = actionEvent.getDataContext();
        PsiDirectory psiDirectory = (PsiDirectory) LangDataKeys.PSI_ELEMENT.getData(dataContext);
        VirtualFile virtualFile = psiDirectory.findFile(className + ".java").getVirtualFile();
        if (virtualFile != null) {
            virtualFile.refresh(false, false);
            // 在编辑器中打开新文件
            FileEditorManager.getInstance(project).openFile(virtualFile, true);
        }
    }
}
