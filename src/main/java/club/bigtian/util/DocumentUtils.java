package club.bigtian.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.intellij.psi.JavaPsiFacade;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiElementFactory;
import com.intellij.psi.PsiJavaDocumentedElement;
import com.intellij.psi.javadoc.PsiDocComment;

public class DocumentUtils {

    /**
     * 创建注释
     *
     * @param element
     * @param comment
     */
    public static PsiDocComment createDocument(PsiElement element, String comment) {
        PsiElementFactory elementFactory = JavaPsiFacade.getElementFactory(element.getProject());
        return elementFactory.createDocCommentFromText("\n/**\n* " + comment + "\n*/");
    }

    /**
     * 创建注释,并追加到元素前面
     *
     * @param element
     * @param comment
     */
    public static void createDocumentAppendBefore(PsiJavaDocumentedElement element, String comment) {
        if (StrUtil.isEmpty(comment) || ObjectUtil.isNotNull(element.getDocComment())) {
            return;
        }
        PsiElementFactory elementFactory = JavaPsiFacade.getElementFactory(element.getProject());
        PsiDocComment commentFromText = elementFactory.createDocCommentFromText("\n/**\n* " + comment + "\n*/");
        element.addBefore(commentFromText, element.getFirstChild());
    }

    /**
     * 创建注释,并追加到元素后面
     *
     * @param element
     * @param comment
     */
    public static void createDocumentAppendAfter(PsiJavaDocumentedElement element, String comment) {
        if (StrUtil.isEmpty(comment) || ObjectUtil.isNotNull(element.getDocComment())) {
            return;
        }

        PsiElementFactory elementFactory = JavaPsiFacade.getElementFactory(element.getProject());
        PsiDocComment commentFromText = elementFactory.createDocCommentFromText("\n/**\n* " + comment + "\n*/");
        element.addAfter(commentFromText, element.getLastChild());
    }

}
