package club.bigtian.util;

import club.bigtian.constant.SysConstant;
import club.bigtian.dto.TableDTO;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.StringWriter;

public class DDlUtl {
    private static void renderTemplate(TableDTO tableInfo) {
        String templateName = "domain.java.vm";
        String tablePre = ObjectUtil.defaultIfBlank(null, "");
        String className = StrUtil.upperFirst(StrUtil.toCamelCase(tableInfo.getTableName().replace(tablePre, "")));
        // 渲染模板
        StringWriter sw = new StringWriter();
        Velocity.init();
        Template tpl = Velocity.getTemplate(templateName, SysConstant.UTF8);
        VelocityContext context = new VelocityContext();
        context.put("className", className);
        context.put("tableName", tableInfo.getTableName());
        context.put("columns", tableInfo.getColumnList());
        context.put("tableComment", tableInfo.getTableComment());
//        context.put("data", dataBox.isSelected());
//        context.put("mybatisPlus", mybatisPlusBox.isSelected());
//        context.put("swagger", swagger.isSelected());
        context.put("data", true);
        context.put("mybatisPlus", true);
        context.put("swagger", true);
        context.put("author", "bigtian");
        tpl.merge(context, sw);
        System.out.println(sw.toString());
//        createClass( sw.toString(), className);
    }
    public static void main(String[] args) {
        TableDTO tableInfo = JSONUtil.toBean("{\"tableName\":\"gen_table_column\",\"tableComment\":\"代码生成业务表字段\",\"columnList\":[{\"fieldName\":\"column_id\",\"fieldType\":\"long\",\"fieldComment\":\"编号\",\"isPrimaryKey\":true},{\"fieldName\":\"table_id\",\"fieldType\":\"String\",\"fieldComment\":\"归属表编号\",\"isPrimaryKey\":false},{\"fieldName\":\"column_name\",\"fieldType\":\"String\",\"fieldComment\":\"列名称\",\"isPrimaryKey\":false},{\"fieldName\":\"column_comment\",\"fieldType\":\"String\",\"fieldComment\":\"列描述\",\"isPrimaryKey\":false},{\"fieldName\":\"column_type\",\"fieldType\":\"String\",\"fieldComment\":\"列类型\",\"isPrimaryKey\":false},{\"fieldName\":\"java_type\",\"fieldType\":\"String\",\"fieldComment\":\"JAVA类型\",\"isPrimaryKey\":false},{\"fieldName\":\"java_field\",\"fieldType\":\"String\",\"fieldComment\":\"JAVA字段名\",\"isPrimaryKey\":false},{\"fieldName\":\"is_pk\",\"fieldType\":\"String\",\"fieldComment\":\"是否主键（1是）\",\"isPrimaryKey\":false},{\"fieldName\":\"is_increment\",\"fieldType\":\"String\",\"fieldComment\":\"是否自增（1是）\",\"isPrimaryKey\":false},{\"fieldName\":\"is_required\",\"fieldType\":\"String\",\"fieldComment\":\"是否必填（1是）\",\"isPrimaryKey\":false},{\"fieldName\":\"is_insert\",\"fieldType\":\"String\",\"fieldComment\":\"是否为插入字段（1是）\",\"isPrimaryKey\":false},{\"fieldName\":\"is_edit\",\"fieldType\":\"String\",\"fieldComment\":\"是否编辑字段（1是）\",\"isPrimaryKey\":false},{\"fieldName\":\"is_list\",\"fieldType\":\"String\",\"fieldComment\":\"是否列表字段（1是）\",\"isPrimaryKey\":false},{\"fieldName\":\"is_query\",\"fieldType\":\"String\",\"fieldComment\":\"是否查询字段（1是）\",\"isPrimaryKey\":false},{\"fieldName\":\"query_type\",\"fieldType\":\"String\",\"fieldComment\":\"查询方式（等于、不等于、大于、小于、范围）\",\"isPrimaryKey\":false},{\"fieldName\":\"html_type\",\"fieldType\":\"String\",\"fieldComment\":\"显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）\",\"isPrimaryKey\":false},{\"fieldName\":\"dict_type\",\"fieldType\":\"String\",\"fieldComment\":\"字典类型\",\"isPrimaryKey\":false},{\"fieldName\":\"sort\",\"fieldType\":\"int\",\"fieldComment\":\"排序\",\"isPrimaryKey\":false},{\"fieldName\":\"create_by\",\"fieldType\":\"String\",\"fieldComment\":\"创建者\",\"isPrimaryKey\":false},{\"fieldName\":\"create_time\",\"fieldType\":\"Date\",\"fieldComment\":\"创建时间\",\"isPrimaryKey\":false},{\"fieldName\":\"update_by\",\"fieldType\":\"String\",\"fieldComment\":\"更新者\",\"isPrimaryKey\":false},{\"fieldName\":\"update_time\",\"fieldType\":\"Date\",\"fieldComment\":\"更新时间\",\"isPrimaryKey\":false}],\"packageSet\":[\"java.util\"]}\n", TableDTO.class);
        renderTemplate(tableInfo);
    }

}
