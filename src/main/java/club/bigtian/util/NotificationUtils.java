package club.bigtian.util;

import club.bigtian.constant.SysConstant;
import club.bigtian.dialogs.WaringDialog;
import club.bigtian.functions.SimpleFunction;
import com.intellij.notification.Notification;
import com.intellij.notification.NotificationType;
import com.intellij.notification.Notifications;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.psi.PsiDirectory;

/**
 * 通知工具类
 */
public class NotificationUtils {

    public static void notifySuccess(String content, String title, Project project) {
        Messages.showMessageDialog(
                content, // 弹框内容
                title,   // 弹框标题
                Messages.getInformationIcon() // 使用的信息图标
        );
        // Notification notification = new Notification(
        //         SysConstant.NOTICE_GROUP_ID,
        //         title,
        //         content,
        //         NotificationType.INFORMATION
        // );
        // // 在屏幕右下角显示通知
        // Notifications.Bus.notify(notification, project);
//        // 可选：激活项目窗口
//        WindowManager.getInstance().getFrame(project).toFront();
    }

    public static void notifyWarning(String content, String title, Project project) {
        Notification notification = new Notification(
                SysConstant.NOTICE_GROUP_ID,
                title,
                content,
                NotificationType.WARNING
        );
        // 在屏幕右下角显示通知
        Notifications.Bus.notify(notification, project);
    }
    public static void notifyError(String content, String title, Project project) {
        Notification notification = new Notification(
                SysConstant.NOTICE_GROUP_ID,
                title,
                content,
                NotificationType.ERROR
        );
        // 在屏幕右下角显示通知
        Notifications.Bus.notify(notification, project);
    }

    /**
     * 显示警告对话框
     *
     * @param project
     * @param fileName
     * @param directory
     * @param simpleFunction
     */
    public void showNotification(Project project, String fileName, PsiDirectory directory, SimpleFunction simpleFunction) {
        WaringDialog dialog = new WaringDialog(project, fileName, () -> {
            simpleFunction.apply();
        });
        dialog.show();
    }
}
