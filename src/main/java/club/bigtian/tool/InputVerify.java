package club.bigtian.tool;

import com.intellij.openapi.project.Project;
import com.intellij.ui.components.JBTextField;

import javax.swing.*;

public class InputVerify {
    private JBTextField textField1;
    private JPanel mainPanel;
    private JButton checkBtn;
    private static final String MESSAGE = "请填写";

    public InputVerify(Project project) {
        checkBtn.addActionListener(e -> {
//            textField1.putClientProperty(DarculaUIUtil.COMPACT_PROPERTY, Boolean.TRUE);
//            textField1.putClientProperty("JComponent.outline", "error");
//            new ValidationInfo("The host cannot be reached", textField1).withOKEnabled();
            // Components initialization
            // new ComponentValidator(project).withValidator(v -> {
            //     String pt = textField1.getText();
            //     if (StringUtil.isNotEmpty(pt)) {
            //         try {
            //             int portValue = Integer.parseInt(pt);
            //             if (portValue >= 0 && portValue <= 65535) {
            //                 v.updateInfo(null);
            //             } else {
            //                 v.updateInfo(new ValidationInfo(MESSAGE, textField1));
            //             }
            //         } catch (NumberFormatException nfe) {
            //             v.updateInfo(new ValidationInfo(MESSAGE, textField1));
            //         }
            //     } else {
            //         v.updateInfo(null);
            //     }
            // }).installOn(textField1);
            //
            // textField1.getDocument().addDocumentListener(new DocumentAdapter() {
            //     @Override
            //     protected void textChanged(@NotNull DocumentEvent e) {
            //         ComponentValidator.getInstance(textField1).ifPresent(v -> v.revalidate());
            //     }
            // });
//            textField1.getDocument().addDocumentListener(new DocumentAdapter() {
//                @Override
//                protected void textChanged(@NotNull DocumentEvent e) {
//                    Object op = textField1.getText().contains(".") ? "error": null;
//                    textField1.putClientProperty("JComponent.outline", op);
//                }
//            });
        });
    }


    public JPanel getMainPanel() {
        return mainPanel;
    }


    private void createUIComponents() {
        textField1 = new JBTextField();

    }
}
