<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="club.bigtian.tool.WorkFlowTemplateContent">
  <grid id="27dc6" binding="mainPanel" layout-manager="GridLayoutManager" row-count="3" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="0" left="0" bottom="0" right="0"/>
    <constraints>
      <xy x="20" y="20" width="685" height="400"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <grid id="3a5a5" layout-manager="GridLayoutManager" row-count="1" column-count="6" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <component id="6b909" class="javax.swing.JLabel">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="模板名称："/>
            </properties>
          </component>
          <component id="26678" class="javax.swing.JTextField" binding="searchTF">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="150" height="-1"/>
              </grid>
            </constraints>
            <properties>
              <text value=""/>
            </properties>
          </component>
          <component id="3019c" class="javax.swing.JButton" binding="searchBtn">
            <constraints>
              <grid row="0" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="搜索"/>
            </properties>
          </component>
          <component id="10859" class="javax.swing.JLabel">
            <constraints>
              <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="类名："/>
            </properties>
          </component>
          <component id="8cc12" class="javax.swing.JTextField" binding="textField1" default-binding="true">
            <constraints>
              <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="150" height="-1"/>
              </grid>
            </constraints>
            <properties/>
          </component>
          <component id="f0fce" class="javax.swing.JComboBox" binding="comboBox">
            <constraints>
              <grid row="0" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <editable value="true"/>
            </properties>
          </component>
        </children>
      </grid>
      <grid id="33621" layout-manager="GridLayoutManager" row-count="1" column-count="3" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <component id="2baf0" class="javax.swing.JButton" binding="exportBtn">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="导出"/>
            </properties>
          </component>
          <component id="8d928" class="javax.swing.JButton" binding="saveBtn">
            <constraints>
              <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="保存"/>
            </properties>
          </component>
          <component id="8938" class="javax.swing.JButton" binding="clearBtn">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="清空"/>
            </properties>
          </component>
        </children>
      </grid>
      <scrollpane id="39465" binding="scrollPane">
        <constraints>
          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="7" hsize-policy="7" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <component id="676ac" class="javax.swing.JTable" binding="dataTable">
            <constraints/>
            <properties>
              <autoResizeMode value="4"/>
              <toolTipText value="撒旦法时代"/>
            </properties>
          </component>
        </children>
      </scrollpane>
    </children>
  </grid>
</form>
