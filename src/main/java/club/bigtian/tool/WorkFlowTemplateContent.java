package club.bigtian.tool;

import club.bigtian.render.MyComboBoxRenderer;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;

public class WorkFlowTemplateContent {
    private JTextField searchTF;
    private JButton saveBtn;
    private JButton searchBtn;
    private JPanel mainPanel;
    private JButton clearBtn;
    private JButton exportBtn;
    private JTable dataTable;
    private JTextField textField1;
    private JScrollPane scrollPane;
    private JComboBox comboBox;
    /**
     * 创建一个表头
     */
    String[] HEADER = {"First Name", "Last Name", "Sport", "# of Years", "Vegetarian"};
    /**
     * 创建数据便于演示
     */
    Object[][] TABLE_DATA = {
            {"<PERSON>", "<PERSON>", "Snowboarding", 5, false},
            {"<PERSON>", "Doe", "Rowing", 3, true},
            {"Sue", "Black", "Knitting", 2, false},
            {"<PERSON>", "<PERSON>", "Speed reading", 20, true},
            {"<PERSON>", "<PERSON>", "Pool", 10, false}
    };

    public WorkFlowTemplateContent() {

        DefaultTableModel DEFAULT_TABLE_MODEL = new DefaultTableModel(TABLE_DATA, HEADER);
        dataTable.setModel(DEFAULT_TABLE_MODEL);
        dataTable.setVisible(true);
        saveBtn.addActionListener(actionEvent -> System.out.println("saveBtn"));
        searchBtn.addActionListener(actionEvent -> System.out.println("searchBtn"));
        clearBtn.addActionListener(actionEvent -> {

        });
        exportBtn.addActionListener(actionEvent -> {

        });
        saveBtn.addActionListener(actionEvent -> {

        });
        comboBox.addActionListener(actionEvent -> {
            System.out.println(actionEvent);
        });
        comboBox.addItem("测试");
        comboBox.setRenderer(new MyComboBoxRenderer());
    }

    public JPanel getMainPanel() {
        return mainPanel;
    }


}
