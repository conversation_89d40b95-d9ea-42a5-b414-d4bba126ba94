package club.bigtian.tool;

import club.bigtian.dto.FlowEnvironment;
import club.bigtian.persistent.PluginSettings;
import club.bigtian.util.NotificationUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.ui.table.JBTable;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.List;

/**
 * 流程环境配置对话框
 */
public class FlowEnvironmentConfigDialog extends DialogWrapper {

    private final AnActionEvent anActionEvent;
    private JBTable table;
    private DefaultTableModel model;
    private final String[] columnNames = {"环境名称", "环境标识", "环境URL", "用户名", "密码", "网关账号", "网关密码"};

    public FlowEnvironmentConfigDialog(AnActionEvent anActionEvent) {
        super(true);
        this.anActionEvent = anActionEvent;
        init();
        setTitle("流程环境配置");
        setSize(900, 400);
    }

    @Override
    protected JComponent createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 创建表格
        model = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return true;
            }
        };
        table = new JBTable(model);
        table.setRowHeight(30);
        
        // 加载已有环境配置
        loadEnvironments();
        
        JScrollPane scrollPane = new JScrollPane(table);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 添加底部按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton addButton = new JButton("添加");
        JButton deleteButton = new JButton("删除");
        
        addButton.addActionListener(e -> {
            model.addRow(new Object[]{"新环境", "env", "http://", "admin", "000000", "gateway", "gateway123"});
        });
        
        deleteButton.addActionListener(e -> {
            int selectedRow = table.getSelectedRow();
            if (selectedRow >= 0) {
                model.removeRow(selectedRow);
            } else {
                NotificationUtils.notifyWarning("请选择要删除的行", "提示", anActionEvent.getProject());
            }
        });
        
        buttonPanel.add(addButton);
        buttonPanel.add(deleteButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private void loadEnvironments() {
        PluginSettings instance = PluginSettings.getInstance();
        String flowEnvironments = instance.getState().flowEnvironments;
        
        if (StrUtil.isNotEmpty(flowEnvironments)) {
            List<FlowEnvironment> environments = JSONUtil.toList(flowEnvironments, FlowEnvironment.class);
            for (FlowEnvironment env : environments) {
                model.addRow(new Object[]{
                        env.getName(),
                        env.getEnv(),
                        env.getUrl(),
                        env.getUsername(),
                        env.getPassword(),
                        env.getGateWayAccount(),
                        env.getGateWayPwd()
                });
            }
        }
    }

    @Override
    protected void doOKAction() {
        // 保存配置
        PluginSettings instance = PluginSettings.getInstance();
        PluginSettings.State state = instance.getState();
        
        int rowCount = model.getRowCount();
        FlowEnvironment[] environments = new FlowEnvironment[rowCount];
        
        for (int i = 0; i < rowCount; i++) {
            String name = (String) model.getValueAt(i, 0);
            String env = (String) model.getValueAt(i, 1);
            String url = (String) model.getValueAt(i, 2);
            String username = (String) model.getValueAt(i, 3);
            String password = (String) model.getValueAt(i, 4);
            String gateWayAccount = (String) model.getValueAt(i, 5);
            String gateWayPwd = (String) model.getValueAt(i, 6);

            environments[i] = new FlowEnvironment(name, env, url, username, password, gateWayAccount, gateWayPwd);
        }
        
        state.flowEnvironments = JSONUtil.toJsonStr(environments);
        instance.loadState(state);
        
        NotificationUtils.notifySuccess("环境配置保存成功", "提示", anActionEvent.getProject());
        super.doOKAction();
    }
} 