package club.bigtian.tool;

import club.bigtian.constant.SysConstant;
import club.bigtian.dto.ApplicationDto;
import club.bigtian.persistent.PluginSettings;
import club.bigtian.util.NotificationUtils;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.intellij.openapi.actionSystem.AnActionEvent;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class InterntionConfigDialog extends JDialog {
    private JPanel contentPane;
    private JButton buttonOK;
    private JButton buttonCancel;
    private JScrollPane scrollPane;
    private AnActionEvent anActionEvent;
    JPanel panel;

    public InterntionConfigDialog(AnActionEvent anActionEvent) {
        setTitle("国际租户配置");
        setSize(600, 600);
        setContentPane(contentPane);
        setModal(true);
        getRootPane().setDefaultButton(buttonOK);
        this.anActionEvent = anActionEvent;
        buttonOK.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                onOK();
            }
        });

        buttonCancel.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                onCancel();
            }
        });

        // call onCancel() when cross is clicked
        setDefaultCloseOperation(DO_NOTHING_ON_CLOSE);
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                onCancel();
            }
        });

        // call onCancel() on ESCAPE
        contentPane.registerKeyboardAction(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                onCancel();
            }
        }, KeyStroke.getKeyStroke(KeyEvent.VK_ESCAPE, 0), JComponent.WHEN_ANCESTOR_OF_FOCUSED_COMPONENT);
        getApplicationList(scrollPane);
    }

    public void getApplicationList(JScrollPane jScrollPane) {
        panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS)); // Set BoxLayout for vertical alignment
        String pluginWorkInterTenant = PluginSettings.getInstance().getState().pluginWorkInterTenant;
        JSONArray tenantArr = JSONUtil.parseArray(pluginWorkInterTenant);

        String result = HttpRequest.get("http://*************/sys-web-jwt/application/list?appName=&appType=&busiType=&pageNum=1&pageSize=9999&orderByColumns=T1.sort,T1.create_time")
                .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIya3FTbmE1TmoweTBmQzhETEJPd1dVdmxuRWNyZUQ4NiJ9.0iPPXwIiMHdQ0UVJ_bL5SYC4y_FLu6pGaJzmu0WV1Ao")
                .execute()
                .body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        List<ApplicationDto> application = jsonObject.getBeanList("records", ApplicationDto.class);
        for (ApplicationDto dto : application) {
            JCheckBox checkBox = new JCheckBox(dto.getAppName());
            checkBox.setSelected(tenantArr.contains(dto.getAppId()));
            checkBox.setName(dto.getAppId());
            panel.add(checkBox);
        }

        jScrollPane.setViewportView(panel); // Set the panel as the viewport view of the JScrollPane
        panel.revalidate(); // Refresh the panel to apply the new layout
        panel.repaint();
    }

    private Set<String> getSelectTenant() {
        Set<String> tenantSet = Arrays.stream(panel.getComponents())
                .filter(
                        component -> component instanceof JCheckBox)
                .filter(component -> ((JCheckBox) component).isSelected())
                .map(Component::getName)
                .collect(Collectors.toSet());
        return tenantSet;
    }

    private void onOK() {
        Set<String> selectTenant = getSelectTenant();
        PluginSettings pluginSettings = PluginSettings.getInstance();
        PluginSettings.State state = pluginSettings.getState();

        JSONObject settings = JSONUtil.createObj();
        settings.set(SysConstant.INTERNTION_TENANT, selectTenant);
        state.pluginWorkInterTenant = JSONUtil.toJsonStr(selectTenant);
        pluginSettings.loadState(state);
        NotificationUtils.notifySuccess("配置保存成功", "提示", anActionEvent.getProject());
        // add your code here
        dispose();
    }

    private void onCancel() {
        // add your code here if necessary
        dispose();
    }

    private void createUIComponents() {
        // TODO: place custom component creation code here
    }
}
