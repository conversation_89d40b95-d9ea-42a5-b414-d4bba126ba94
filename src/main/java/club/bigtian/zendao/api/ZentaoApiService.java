package club.bigtian.zendao.api;

import club.bigtian.zendao.entity.*;
import club.bigtian.zendao.settings.ZentaoSettings;
import club.bigtian.zendao.settings.ZentaoGlobalSettings;
import club.bigtian.zendao.settings.CrossIdeZentaoSettings;
import club.bigtian.zendao.settings.IZentaoSettings;
import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.map.BiMap;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.intellij.notification.NotificationGroupManager;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.project.Project;

import java.util.*;
import java.util.stream.Collectors;

public class ZentaoApiService {

    private final Project project;
    private final ZentaoSettings projectSettings;
    private final ZentaoGlobalSettings globalSettings;
    private final CrossIdeZentaoSettings crossIdeSettings;
    private String token;

    /**
     * 禅道API响应结果状态
     */
    private static final String API_STATUS_SUCCESS = "success";

    // 用户任务时间缓存，key为用户名，value为最后任务的结束时间
    private static final Map<String, Date> USER_TASK_TIME_CACHE = new HashMap<>();

    // 用户映射缓存，30分钟过期
    private static final Cache<String, BiMap<String, String>> USER_CACHE = CacheUtil.newTimedCache(30 * 60 * 1000);
    private static final String USER_CACHE_KEY = "zentao_users";

    /**
     * 创建禅道API服务实例
     *
     * @param project 项目
     */
    public ZentaoApiService(Project project) {
        this.project = project;
        this.projectSettings = project != null ? ZentaoSettings.getInstance(project) : null;
        this.globalSettings = ZentaoGlobalSettings.getInstance();
        this.crossIdeSettings = CrossIdeZentaoSettings.getInstance();
    }

    /**
     * 获取当前生效的配置
     * 优先级：项目级配置 > 跨IDE全局配置 > IDE级别全局配置
     *
     * @return 当前生效的配置
     */
    private IZentaoSettings getEffectiveSettings() {
        // 优先使用项目级配置
        if (projectSettings != null && projectSettings.isConfigured()) {
            return projectSettings;
        }

        // 其次使用跨IDE全局配置
        if (crossIdeSettings != null && crossIdeSettings.isConfigured()) {
            return crossIdeSettings;
        }

        // 最后使用IDE级别全局配置
        return globalSettings;
    }

    /**
     * 获取禅道API认证令牌
     *
     * @return 认证令牌实体
     */
    public String getToken() {
        IZentaoSettings settings = getEffectiveSettings();
        // 检查设置是否已完成
        if (!settings.isConfigured()) {
            showErrorNotification("请先配置禅道设置");
            return null;
        }

        // 如果已有有效token则直接返回
        if (token != null) {
            return token;
        }

        try {
            // 构建登录参数
            Map<String, Object> params = new HashMap<>();
            params.put("account", settings.getUsername());
            params.put("password", settings.getPassword());

            // 发送登录请求
            HttpResponse response = HttpRequest.post(settings.getZentaoUrl() + "api.php/v1/tokens")
                    .body(JSONUtil.toJsonStr(params))
                    .execute();

            // 检查响应状态
            if (response.getStatus() != 201) {
                showErrorNotification("禅道登录失败: 服务器响应状态码 " + response.getStatus());
                return null;
            }

            // 解析响应JSON
            String responseBody = response.body();
            JSONObject jsonResult = JSON.parseObject(responseBody);

            this.token = jsonResult.getString("token");
            return this.token;
        } catch (Exception e) {
            showErrorNotification("禅道登录异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取我的任务列表
     *
     * @return 任务列表
     */
    public List<ZentaoTask> getMyTasks() {
        String token = getToken();
        if (token == null) {
            return new ArrayList<>();
        }
        try {
            // 构建请求URL
            String apiUrl = getEffectiveSettings().getZentaoUrl() + "/my-work-task-assignedTo.json";
            // 发送请求
            HttpResponse response = HttpRequest.get(apiUrl.toString())
                    .header("Token", token)
                    .execute();
            return JSON.parseObject(response.body()).getJSONObject("data").getJSONArray("tasks")
                    .toJavaList(ZentaoTask.class);
        } catch (Exception e) {
            showErrorNotification("获取任务列表异常: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取我的Bug列表
     *
     * 
     * @return Bug列表
     */
    public Collection<ZentaoBug> getMyBugs() {
        String token = getToken();
        if (token == null) {
            return new ArrayList<>();
        }

        try {
            // 构建请求URL
            StringBuilder apiUrl = new StringBuilder(getEffectiveSettings().getZentaoUrl()).append("/my-work-bug-assignedTo.json");

            // 发送请求
            HttpResponse response = HttpRequest.get(apiUrl.toString())
                    .header("Token", token)
                    .execute();

            JSON.parseObject(response.body()).getJSONObject("data").getJSONArray("bugs");
            JSONObject[] array = JSON.parseObject(response.body()).getJSONObject("data").getJSONObject("bugs").values()
                    .toArray(new JSONObject[0]);
            List<ZentaoBug> bugsList = Arrays.stream(array)
                    .map(json -> JSON.parseObject(json.toJSONString(), ZentaoBug.class))
                    .collect(Collectors.toList());
            return bugsList;
        } catch (Exception e) {
            showErrorNotification("获取Bug列表异常: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    public ZentaoTask getTaskDetail(int taskId) {
        String token = getToken();
        if (token == null) {
            return null;
        }

        try {
            // 构建请求URL
            String apiUrl = getEffectiveSettings().getZentaoUrl() + "/api.php/v1/tasks/" + taskId;

            // 发送请求
            HttpResponse response = HttpRequest.get(apiUrl)
                    .header("Token", token)
                    .execute();
            // 解析响应JSON
            String responseBody = response.body();
            // 直接将JSON转换为ZentaoTask对象
            ZentaoTask task = JSON.parseObject(responseBody, ZentaoTask.class);
            return task;
        } catch (Exception e) {
            showErrorNotification("获取任务详情异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取Bug详情
     *
     * @param bugId Bug ID
     * @return Bug详情
     */
    public ZentaoBug getBugDetail(int bugId) {
        String token = getToken();
        if (token == null) {
            return null;
        }

        try {
            // 构建请求URL
            String apiUrl = getEffectiveSettings().getZentaoUrl() + "/api.php/v1/bugs/" + bugId;

            // 发送请求
            HttpResponse response = HttpRequest.get(apiUrl)
                    .header("Token", token)
                    .execute();
            // 解析响应JSON
            String responseBody = response.body();
            return JSON.parseObject(responseBody, ZentaoBug.class);
        } catch (Exception e) {
            showErrorNotification("获取Bug详情异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取产品列表
     *
     * @return 产品列表
     */
    public List<ZentaoProduct> getProducts() {
        String token = getToken();
        if (token == null) {
            return new ArrayList<>();
        }

        try {
            // 构建请求URL
            String apiUrl = getEffectiveSettings().getZentaoUrl() + "/api.php/v1/products";

            // 发送请求
            HttpResponse response = HttpRequest.get(apiUrl)
                    .header("Token", token)
                    .execute();

            // 解析响应JSON
            String responseBody = response.body();
            JSONObject jsonResult = JSON.parseObject(responseBody);

            // 直接将JSON数组转换为List<ZentaoProduct>
            List<ZentaoProduct> productsList = jsonResult.getList("products",
                    ZentaoProduct.class);

            return productsList;
        } catch (Exception e) {
            showErrorNotification("获取产品列表异常: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 显示错误通知
     *
     * @param message 通知消息
     */
    private void showErrorNotification(String message) {
        if (project != null) {
            NotificationGroupManager.getInstance()
                    .getNotificationGroup("Git Commit Helper")
                    .createNotification(message, NotificationType.ERROR)
                    .notify(project);
        }
    }

    /**
     * 获取用户下一个可用的任务时间
     * 
     * @param username  用户名
     * @param taskHours 任务工时
     * @return 任务开始时间和结束时间
     */
    private Map<String, Date> calculateTaskTime(String username, float taskHours) {
        Calendar calendar = Calendar.getInstance();

        // 获取用户最后任务时间，如果没有则从当前时间开始
        Date lastTaskTime = USER_TASK_TIME_CACHE.getOrDefault(username, new Date());
        calendar.setTime(lastTaskTime);

        // 如果是过去的时间，从当前时间重新开始
        if (calendar.getTime().before(new Date())) {
            calendar.setTime(new Date());
            // 如果当前时间超过17:00，从明天9:00开始
            if (calendar.get(Calendar.HOUR_OF_DAY) >= 17) {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 9);
                calendar.set(Calendar.MINUTE, 0);
            }
            // 如果当前时间早于9:00，从今天9:00开始
            else if (calendar.get(Calendar.HOUR_OF_DAY) < 9) {
                calendar.set(Calendar.HOUR_OF_DAY, 9);
                calendar.set(Calendar.MINUTE, 0);
            }
        }

        Date startTime = calendar.getTime();

        // 计算结束时间
        float remainingHours = taskHours;
        while (remainingHours > 0) {
            // 如果是周末，跳到下周一
            if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
                calendar.add(Calendar.DAY_OF_MONTH, 2);
                calendar.set(Calendar.HOUR_OF_DAY, 9);
                calendar.set(Calendar.MINUTE, 0);
            } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 9);
                calendar.set(Calendar.MINUTE, 0);
            }

            // 当前时间点到当天17:00的剩余工作时间
            int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
            float todayRemainingHours = currentHour < 17 ? 17 - currentHour : 0;

            if (remainingHours <= todayRemainingHours) {
                // 剩余工时不足一天，直接加上剩余工时
                calendar.add(Calendar.MINUTE, Math.round(remainingHours * 60));
                remainingHours = 0;
            } else {
                // 剩余工时超过今天，用完今天的时间后转到明天9:00
                remainingHours -= todayRemainingHours;
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 9);
                calendar.set(Calendar.MINUTE, 0);
            }
        }

        Date endTime = calendar.getTime();

        // 更新缓存
        USER_TASK_TIME_CACHE.put(username, endTime);

        Map<String, Date> result = new HashMap<>();
        result.put("startTime", startTime);
        result.put("endTime", endTime);
        return result;
    }

    /**
     * 创建新任务
     *
     * @param task 任务实体
     * @return 创建的任务ID，如果创建失败返回null
     */
    public Integer createTask(Task task, int projectId) {
        String token = getToken();
        if (token == null) {
            return null;
        }

        try {
            // 计算任务工时
            float taskHours = 8.0f; // 默认8小时
            if (task.getTaskDuration() != null && !task.getTaskDuration().isEmpty()) {
                taskHours = Float.parseFloat(task.getTaskDuration());
            }

            // 计算任务时间
            Map<String, Date> taskTime = calculateTaskTime(task.getAssignment(), taskHours);
            java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");

            // 构建任务参数
            Map<String, Object> params = new HashMap<>();
            getUsernameByRealName("");
            // 必填参数
            params.put("name", task.getTitle()); // 任务名称
            params.put("desc", task.getTitle()); // 任务描述
            params.put("type", "devel"); // 默认设置为开发类型
            params.put("assignedTo", getUsernameByRealName(task.getAssignment())); // 指派给
            params.put("estStarted", dateFormat.format(taskTime.get("startTime"))); // 预计开始时间
            params.put("deadline", dateFormat.format(taskTime.get("endTime"))); // 预计结束时间
            // params.put("openedBy", "daijunxiong"); // 创建人
            params.put("openedDate", dateFormat.format(new Date())); // 创建时间

            // 可选参数
            if (task.getRequirementId() != null) {
                params.put("story", task.getRequirementId()); // 关联需求
            }
            params.put("estimate", taskHours); // 预计工时

            // 设置默认值
            params.put("pri", 3); // 默认优先级为普通
            params.put("status", "wait"); // 设置初始状态为等待

            // 发送创建任务请求
            String apiUrl = getEffectiveSettings().getZentaoUrl() + StrUtil.format("/api.php/v1/executions/{}/tasks", projectId);
            HttpResponse response = HttpRequest.post(apiUrl)
                    .header("Token", token)
                    .body(JSON.toJSONString(params))
                    .execute();

            // 解析响应获取创建的任务ID
            JSONObject result = JSON.parseObject(response.body());
            return result.getInteger("id");
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据用户姓名查询用户名，使用BiMap实现双向映射
     *
     * @param realName 用户姓名
     * @return 用户名和真实姓名的映射Map，如果未找到则返回空Map
     */
    public String getUsernameByRealName(String realName) {
        try {
            // 尝试从缓存获取用户映射
            BiMap<String, String> userMap = USER_CACHE.get(USER_CACHE_KEY);

            if (userMap == null) {
                String token = getToken();
                if (token == null) {
                    return "";
                }

                // 构建请求URL并发送请求
                String apiUrl = getEffectiveSettings().getZentaoUrl() + "/my-work-bug-assignedTo.json";
                HttpResponse response = HttpRequest.get(apiUrl)
                        .header("Token", token)
                        .execute();

                // 解析响应数据
                HashMap<String, String> rawData = JSON.parseObject(response.body())
                        .getJSONObject("data")
                        .getJSONObject("users")
                        .toJavaObject(HashMap.class);

                // 创建BiMap并填充数据
                userMap = new BiMap<>(rawData);

                // 存入缓存
                USER_CACHE.put(USER_CACHE_KEY, userMap);
            }

            // 如果传入realName为空，返回整个映射
            if (StrUtil.isEmpty(realName)) {
                return "";
            }

            // 通过realName查找username
            return userMap.getInverse().get(realName);

        } catch (Exception e) {
            showErrorNotification("获取用户列表异常: " + e.getMessage());
            return "";
        }
    }

    /**
     * 获取今日动态
     * 只返回bug已解决和task已完成的动态
     *
     * @return 动态列表
     */
    public List<Dynamic> getTodayDynamic() {
        String token = getToken();
        if (token == null) {
            return new ArrayList<>();
        }
        User user = getUserInfo();
        try {
            // 构建请求URL
            String apiUrl = StrUtil.format("{}/company-dynamic-today--0--no-{}-0-0-0-date_desc.json",
                    getEffectiveSettings().getZentaoUrl(), user.getId());

            // 发送请求
            HttpResponse response = HttpRequest.get(apiUrl)
                    .header("Token", token)
                    .execute();

            // 解析响应JSON
            JSONObject dynamicData = JSON.parseObject(response.body()).getJSONObject("data");
            if (dynamicData == null || dynamicData.getJSONObject("dateGroups") == null) {
                return new ArrayList<>();
            }

            List<Dynamic> dynamics = new ArrayList<>();
            JSONObject dateGroups = dynamicData.getJSONObject("dateGroups");

            // 遍历所有日期组的动态数据
            for (String date : dateGroups.keySet()) {
                JSONArray items = dateGroups.getJSONArray(date);
                if (items != null) {
                    for (int i = 0; i < items.size(); i++) {
                        JSONObject item = items.getJSONObject(i);

                        // 只保留特定类型和动作的动态
                        boolean isBugResolved = "bug".equals(item.getString("objectType"))
                                && "resolved".equals(item.getString("action"));
                        boolean isTaskFinished = "task".equals(item.getString("objectType"))
                                && "finished".equals(item.getString("action"));

                        if (isBugResolved || isTaskFinished) {
                            Dynamic dynamic = new Dynamic();
                            dynamic.setId(item.getInteger("id"));
                            dynamic.setType(item.getString("objectType"));
                            dynamic.setAction(item.getString("action"));
                            dynamic.setDate(item.getString("originalDate"));
                            dynamic.setObjectType(item.getString("objectType"));
                            dynamic.setObjectId(item.getInteger("objectID"));
                            dynamic.setActor(item.getString("actor"));
                            dynamic.setComment(StrUtil.isNotEmpty(item.getString("comment")) ? item.getString("comment")
                                    : item.getString("objectName"));
                            dynamic.setExtra(item.getString("extra"));

                            dynamics.add(dynamic);
                        }
                    }
                }
            }

            // 按时间降序排序
            dynamics.sort((a, b) -> {
                try {
                    return java.sql.Timestamp.valueOf(b.getDate()).compareTo(
                            java.sql.Timestamp.valueOf(a.getDate()));
                } catch (Exception e) {
                    return 0;
                }
            });

            return dynamics;
        } catch (Exception e) {
            showErrorNotification("获取今日动态异常: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    public User getUserInfo() {
        String token = getToken();
        if (token == null) {
            return null;
        }

        try {
            // 构建请求URL
            String apiUrl = getEffectiveSettings().getZentaoUrl() + "/api.php/v1/user";

            // 发送请求
            HttpResponse response = HttpRequest.get(apiUrl)
                    .header("Token", token)
                    .execute();

            // 解析响应JSON
            JSONObject jsonResult = JSON.parseObject(response.body());
            if (jsonResult == null || jsonResult.getJSONObject("profile") == null) {
                showErrorNotification("获取用户信息失败: 响应数据为空");
                return null;
            }

            // 将JSON转换为User对象
            return jsonResult.getJSONObject("profile").toJavaObject(User.class);
        } catch (Exception e) {
            showErrorNotification("获取用户信息异常: " + e.getMessage());
            return null;
        }
    }

}