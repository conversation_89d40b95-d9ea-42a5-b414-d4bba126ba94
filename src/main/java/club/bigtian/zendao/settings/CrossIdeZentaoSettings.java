package club.bigtian.zendao.settings;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

/**
 * 跨 IDE 的禅道设置
 * 配置文件存储在用户主目录下，所有 JetBrains IDE 都能访问
 */
public class CrossIdeZentaoSettings implements IZentaoSettings {
    
    private static final String CONFIG_DIR = ".zentao";
    private static final String CONFIG_FILE = "config.json";
    private static CrossIdeZentaoSettings instance;
    
    private String zentaoUrl = "";
    private String username = "";
    private String password = "";
    
    private final Path configPath;
    
    private CrossIdeZentaoSettings() {
        // 构建配置文件路径：~/.zentao/config.json
        String userHome = System.getProperty("user.home");
        this.configPath = Paths.get(userHome, CONFIG_DIR, CONFIG_FILE);
        loadSettings();
    }
    
    /**
     * 获取跨 IDE 配置实例（单例模式）
     */
    public static synchronized CrossIdeZentaoSettings getInstance() {
        if (instance == null) {
            instance = new CrossIdeZentaoSettings();
        }
        return instance;
    }
    
    /**
     * 从配置文件加载设置
     */
    private void loadSettings() {
        try {
            if (Files.exists(configPath)) {
                String content = Files.readString(configPath);
                JSONObject config = JSON.parseObject(content);
                
                this.zentaoUrl = config.getString("zentaoUrl");
                this.username = config.getString("username");
                this.password = config.getString("password");
                
                // 处理 null 值
                if (this.zentaoUrl == null) this.zentaoUrl = "";
                if (this.username == null) this.username = "";
                if (this.password == null) this.password = "";
            }
        } catch (Exception e) {
            // 如果读取失败，使用默认值
            this.zentaoUrl = "";
            this.username = "";
            this.password = "";
        }
    }
    
    /**
     * 保存设置到配置文件
     */
    private void saveSettings() {
        try {
            // 确保目录存在
            Files.createDirectories(configPath.getParent());
            
            // 构建配置 JSON
            JSONObject config = new JSONObject();
            config.put("zentaoUrl", this.zentaoUrl);
            config.put("username", this.username);
            config.put("password", this.password);
            
            // 写入文件
            Files.writeString(configPath, config.toJSONString(), 
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
                
        } catch (IOException e) {
            // 保存失败时静默处理，避免影响用户体验
            System.err.println("Failed to save cross-IDE Zentao settings: " + e.getMessage());
        }
    }
    
    @Override
    public String getZentaoUrl() {
        return zentaoUrl;
    }
    
    @Override
    public void setZentaoUrl(String zentaoUrl) {
        this.zentaoUrl = zentaoUrl != null ? zentaoUrl : "";
        saveSettings();
    }
    
    @Override
    public String getUsername() {
        return username;
    }
    
    @Override
    public void setUsername(String username) {
        this.username = username != null ? username : "";
        saveSettings();
    }
    
    @Override
    public String getPassword() {
        return password;
    }
    
    @Override
    public void setPassword(String password) {
        this.password = password != null ? password : "";
        saveSettings();
    }
    
    @Override
    public boolean isConfigured() {
        return !zentaoUrl.isEmpty() && !username.isEmpty() && !password.isEmpty();
    }
    
    /**
     * 检查配置文件是否存在
     */
    public boolean configFileExists() {
        return Files.exists(configPath);
    }
    
    /**
     * 获取配置文件路径
     */
    public String getConfigFilePath() {
        return configPath.toString();
    }
    
    /**
     * 从另一个配置对象复制设置
     */
    public void copyFrom(IZentaoSettings other) {
        if (other != null) {
            setZentaoUrl(other.getZentaoUrl());
            setUsername(other.getUsername());
            setPassword(other.getPassword());
        }
    }
    
    /**
     * 清空所有配置
     */
    public void clear() {
        setZentaoUrl("");
        setUsername("");
        setPassword("");
    }
}
