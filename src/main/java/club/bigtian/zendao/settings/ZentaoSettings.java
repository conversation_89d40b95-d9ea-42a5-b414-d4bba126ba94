package club.bigtian.zendao.settings;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import com.intellij.openapi.project.Project;
import com.intellij.util.xmlb.XmlSerializerUtil;

/**
 * 禅道设置
 * 用于存储和检索禅道的连接信息
 */
@State(name = "ZentaoSettings", storages = @Storage("zentaoSettings.xml"))
public class ZentaoSettings implements PersistentStateComponent<ZentaoSettings>, IZentaoSettings {

    private String zentaoUrl = "";
    private String username = "";
    private String password = "";

    /**
     * 获取项目的禅道设置实例
     *
     * @param project 项目
     * @return 禅道设置实例
     */
    public static ZentaoSettings getInstance(Project project) {
        return project.getService(ZentaoSettings.class);
    }

    @Override
    public @Nullable ZentaoSettings getState() {
        return this;
    }

    @Override
    public void loadState(@NotNull ZentaoSettings state) {
        XmlSerializerUtil.copyBean(state, this);
    }

    // Getters and Setters
    public String getZentaoUrl() {
        return zentaoUrl;
    }

    public void setZentaoUrl(String zentaoUrl) {
        this.zentaoUrl = zentaoUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 检查设置是否已完成配置
     *
     * @return 是否已配置
     */
    public boolean isConfigured() {
        return !zentaoUrl.isEmpty() && !username.isEmpty() && !password.isEmpty();
    }
}