package club.bigtian.zendao.settings;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import com.intellij.util.xmlb.XmlSerializerUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

@State(
    name = "ZentaoGlobalSettings",
    storages = @Storage("zentao-global-settings.xml")
)
public class ZentaoGlobalSettings implements PersistentStateComponent<ZentaoGlobalSettings>, IZentaoSettings {
    private static final String CONFIG_DIR = ".zentao";
    private static final String CONFIG_FILE = "config.json";
    private static ZentaoGlobalSettings instance;

    private String zentaoUrl = "";
    private String username = "";
    private String password = "";

    private final Path configPath;

    private ZentaoGlobalSettings() {
        // 构建配置文件路径：~/.zentao/config.json
        String userHome = System.getProperty("user.home");
        this.configPath = Paths.get(userHome, CONFIG_DIR, CONFIG_FILE);
        loadFromFile();
    }

    public static ZentaoGlobalSettings getInstance() {
        if (instance == null) {
            synchronized (ZentaoGlobalSettings.class) {
                if (instance == null) {
                    // 先尝试从 ApplicationManager 获取（保持兼容性）
                    try {
                        instance = ApplicationManager.getApplication().getService(ZentaoGlobalSettings.class);
                        if (instance == null) {
                            instance = new ZentaoGlobalSettings();
                        }
                    } catch (Exception e) {
                        instance = new ZentaoGlobalSettings();
                    }
                }
            }
        }
        return instance;
    }

    /**
     * 从文件加载配置
     */
    private void loadFromFile() {
        try {
            if (Files.exists(configPath)) {
                String content = Files.readString(configPath);
                JSONObject config = JSON.parseObject(content);

                this.zentaoUrl = config.getString("zentaoUrl");
                this.username = config.getString("username");
                this.password = config.getString("password");

                // 处理 null 值
                if (this.zentaoUrl == null) this.zentaoUrl = "";
                if (this.username == null) this.username = "";
                if (this.password == null) this.password = "";
            }
        } catch (Exception e) {
            // 如果读取失败，使用默认值
            this.zentaoUrl = "";
            this.username = "";
            this.password = "";
        }
    }

    /**
     * 保存配置到文件
     */
    private void saveToFile() {
        try {
            // 确保目录存在
            Files.createDirectories(configPath.getParent());

            // 构建配置 JSON
            JSONObject config = new JSONObject();
            config.put("zentaoUrl", this.zentaoUrl);
            config.put("username", this.username);
            config.put("password", this.password);

            // 写入文件
            Files.writeString(configPath, config.toJSONString(),
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        } catch (IOException e) {
            // 保存失败时静默处理，避免影响用户体验
            System.err.println("Failed to save Zentao global settings: " + e.getMessage());
        }
    }

    @Override
    public @Nullable ZentaoGlobalSettings getState() {
        return this;
    }

    @Override
    public void loadState(@NotNull ZentaoGlobalSettings state) {
        XmlSerializerUtil.copyBean(state, this);
        // 加载后也从文件读取，确保使用最新的跨IDE配置
        loadFromFile();
    }

    // Getters and Setters
    public String getZentaoUrl() {
        return zentaoUrl;
    }

    public void setZentaoUrl(String zentaoUrl) {
        this.zentaoUrl = zentaoUrl != null ? zentaoUrl : "";
        saveToFile(); // 保存到文件
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username != null ? username : "";
        saveToFile(); // 保存到文件
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password != null ? password : "";
        saveToFile(); // 保存到文件
    }

    /**
     * 检查设置是否已完成配置
     *
     * @return 是否已配置
     */
    public boolean isConfigured() {
        return !zentaoUrl.isEmpty() && !username.isEmpty() && !password.isEmpty();
    }
} 