package club.bigtian.zendao.settings;

import java.awt.Dimension;
import java.util.HashMap;
import java.util.Map;

import javax.swing.JComponent;
import javax.swing.JPanel;

import org.jetbrains.annotations.Nullable;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.intellij.notification.NotificationGroupManager;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.ValidationInfo;
import com.intellij.ui.components.JBCheckBox;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPasswordField;
import com.intellij.ui.components.JBTextField;
import com.intellij.ui.components.JBRadioButton;
import com.intellij.util.ui.FormBuilder;
import com.intellij.util.ui.JBUI;

import javax.swing.ButtonGroup;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;

/**
 * 禅道设置对话框
 * 用于收集并保存禅道的连接信息
 */
public class ZentaoSettingsDialog extends DialogWrapper {

    private final Project project;
    private JBTextField zentaoUrlField;
    private JBTextField usernameField;
    private JBPasswordField passwordField;
    private JBRadioButton projectLevelRadio;
    private JBRadioButton crossIdeRadio;
    private JBRadioButton ideGlobalRadio;
    private final ZentaoSettings projectSettings;
    private final ZentaoGlobalSettings globalSettings;
    private final CrossIdeZentaoSettings crossIdeSettings;

    public ZentaoSettingsDialog(@Nullable Project project) {
        super(project);
        this.project = project;
        this.projectSettings = project != null ? ZentaoSettings.getInstance(project) : null;
        this.globalSettings = ZentaoGlobalSettings.getInstance();
        this.crossIdeSettings = CrossIdeZentaoSettings.getInstance();

        setTitle("禅道设置");
        setSize(450, 280);
        init();
    }

    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 确定当前应该使用哪种配置级别
     */
    private int determineConfigLevel() {
        // 如果项目设置已配置，使用项目级别
        if (projectSettings != null && projectSettings.isConfigured()) {
            return 0; // 项目级别
        }

        // 如果跨IDE设置已配置，使用跨IDE级别
        if (crossIdeSettings != null && crossIdeSettings.isConfigured()) {
            return 1; // 跨IDE级别
        }

        // 默认使用IDE全局级别
        return 2; // IDE全局级别
    }

    @Override
    protected @Nullable JComponent createCenterPanel() {
        // 如果存在全局设置，默认不选中项目级别设置
        boolean useProjectSettings = project != null && projectSettings != null && 
            (!isEmpty(projectSettings.getZentaoUrl()) || 
             !isEmpty(projectSettings.getUsername()) || 
             !isEmpty(projectSettings.getPassword()));
        
        projectLevelCheckBox = new JBCheckBox("使用项目级别设置", useProjectSettings);
        zentaoUrlField = new JBTextField();
        usernameField = new JBTextField();
        passwordField = new JBPasswordField();

        // 根据是否选择项目级别来加载不同的设置
        loadSettings();

        // 添加复选框变更监听器
        projectLevelCheckBox.addActionListener(e -> loadSettings());

        // 使用FormBuilder创建表单布局
        FormBuilder formBuilder = FormBuilder.createFormBuilder()
                .addComponent(projectLevelCheckBox)
                .addSeparator()
                .addLabeledComponent(new JBLabel("禅道地址:"), zentaoUrlField, true)
                .addLabeledComponent(new JBLabel("用户名:"), usernameField, true)
                .addLabeledComponent(new JBLabel("密码:"), passwordField, true)
                .addComponentFillVertically(new JPanel(), 0);

        JPanel panel = formBuilder.getPanel();
        panel.setPreferredSize(new Dimension(400, 180));
        panel.setBorder(JBUI.Borders.empty(10));

        return panel;
    }

    private void loadSettings() {
        if (projectLevelCheckBox.isSelected() && projectSettings != null) {
            // 加载项目级别设置
            zentaoUrlField.setText(projectSettings.getZentaoUrl());
            usernameField.setText(projectSettings.getUsername());
            passwordField.setText(projectSettings.getPassword());
        } else {
            // 加载全局设置
            zentaoUrlField.setText(globalSettings.getZentaoUrl());
            usernameField.setText(globalSettings.getUsername());
            passwordField.setText(globalSettings.getPassword());
        }
    }

    @Override
    protected void doOKAction() {
        // 获取禅道URL并检查是否以/结尾
        String zentaoUrl = zentaoUrlField.getText().trim();
        if (!zentaoUrl.isEmpty() && !zentaoUrl.endsWith("/")) {
            zentaoUrl = zentaoUrl + "/";
        }

        // 根据是否选择项目级别来保存设置
        if (projectLevelCheckBox.isSelected() && projectSettings != null) {
            // 保存项目级别设置
            projectSettings.setZentaoUrl(zentaoUrl);
            projectSettings.setUsername(usernameField.getText().trim());
            projectSettings.setPassword(new String(passwordField.getPassword()));
        } else {
            // 保存全局设置
            globalSettings.setZentaoUrl(zentaoUrl);
            globalSettings.setUsername(usernameField.getText().trim());
            globalSettings.setPassword(new String(passwordField.getPassword()));
        }

        // 显示保存成功通知
        if (project != null) {
            NotificationGroupManager.getInstance()
                    .getNotificationGroup("Git Commit Helper")
                    .createNotification("禅道设置已保存成功", NotificationType.INFORMATION)
                    .notify(project);
        }

        super.doOKAction();
    }

    @Override
    protected @Nullable ValidationInfo doValidate() {
        if (zentaoUrlField.getText().trim().isEmpty()) {
            return new ValidationInfo("禅道地址不能为空", zentaoUrlField);
        }
        if (usernameField.getText().trim().isEmpty()) {
            return new ValidationInfo("用户名不能为空", usernameField);
        }
        if (passwordField.getPassword().length == 0) {
            return new ValidationInfo("密码不能为空", passwordField);
        }

        // 验证登录凭据并获取token
        try {
            // 获取禅道URL并确保以/结尾
            String zentaoUrl = zentaoUrlField.getText().trim();
            if (!zentaoUrl.isEmpty() && !zentaoUrl.endsWith("/")) {
                zentaoUrl = zentaoUrl + "/";
            }

            // 构建登录参数
            Map<String, Object> params = new HashMap<>();
            params.put("account", usernameField.getText().trim());
            params.put("password", new String(passwordField.getPassword()));

            // 发送登录请求
            HttpResponse response = HttpRequest.post(zentaoUrl + "api.php/v1/tokens")
                    .body(JSONUtil.toJsonStr(params))
                    .execute();

            // 检查响应状态
            if (response.getStatus() != 201) {
                return new ValidationInfo("禅道验证失败: 服务器响应状态码 " + response.getStatus(), passwordField);
            }

            // 解析响应JSON
            String responseBody = response.body();
            JSONObject jsonResult = JSON.parseObject(responseBody);

            String token = jsonResult.getString("token");
            if (token == null || token.isEmpty()) {
                return new ValidationInfo("禅道验证失败: 未能获取有效token", passwordField);
            }

            // 验证成功，返回null表示验证通过
            return null;
        } catch (Exception e) {
            return new ValidationInfo("禅道验证失败: " + e.getMessage(), passwordField);
        }
    }

    @Override
    public boolean isOKActionEnabled() {
        // 始终允许点击OK按钮
        return true;
    }
}