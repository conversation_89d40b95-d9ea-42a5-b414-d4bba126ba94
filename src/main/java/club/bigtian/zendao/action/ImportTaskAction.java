package club.bigtian.zendao.action;

import club.bigtian.zendao.api.ZentaoApiService;
import club.bigtian.zendao.entity.Task;
import club.bigtian.zendao.entity.TaskImport;
import club.bigtian.zendao.entity.ZentaoProduct;
import club.bigtian.zendao.settings.ZentaoSettings;
import club.bigtian.zendao.settings.ZentaoGlobalSettings;
import club.bigtian.zendao.settings.ZentaoSettingsDialog;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.util.ObjectUtil;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.*;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.ui.components.JBLabel;
import com.intellij.util.ui.FormBuilder;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 导入禅道任务动作
 * 在Tools菜单中添加一个导入禅道任务的选项
 */
public class ImportTaskAction extends AnAction {

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            return;
        }

        ZentaoSettings projectSettings = ZentaoSettings.getInstance(project);
        ZentaoGlobalSettings globalSettings = ZentaoGlobalSettings.getInstance();
        
        // 优先使用项目级配置，如果项目级配置不存在或未配置，则使用全局配置
        boolean isConfigured = (projectSettings != null && projectSettings.isConfigured()) || globalSettings.isConfigured();
        
        if (!isConfigured) {
            // 如果没有配置，打开设置对话框
            ZentaoSettingsDialog dialog = new ZentaoSettingsDialog(project);
            if (!dialog.showAndGet()) {
                return;
            }
        }

        // 显示导入对话框
        ImportTaskDialog importTaskDialog = new ImportTaskDialog(project);
        importTaskDialog.show();
    }

    /**
     * 导入任务对话框
     */
    private static class ImportTaskDialog extends DialogWrapper {
        private final Project project;
        private final TextFieldWithBrowseButton fileChooser;
        private final ComboBox<ZentaoProduct> productComboBox;
        private final ZentaoApiService zentaoApiService;

        protected ImportTaskDialog(@Nullable Project project) {
            super(project);
            this.project = project;
            this.zentaoApiService = new ZentaoApiService(project);

            // 初始化文件选择器
            fileChooser = new TextFieldWithBrowseButton();

            FileChooserDescriptor descriptor = new FileChooserDescriptor(true, false, false, false, false, false)
                    .withTitle("选择文件")
                    .withDescription("请选择要导入的文件");
            fileChooser.addBrowseFolderListener(project, descriptor);
            // 初始化产品下拉框
            productComboBox = new ComboBox<>();
            productComboBox.setPreferredSize(new Dimension(400, productComboBox.getPreferredSize().height));
            loadProducts();

            setTitle("导入禅道任务");
            init();
        }

        /**
         * 加载产品列表
         */
        private void loadProducts() {
            List<ZentaoProduct> products = zentaoApiService.getProducts();
            DefaultComboBoxModel<ZentaoProduct> model = new DefaultComboBoxModel<>();
            for (ZentaoProduct product : products) {
                model.addElement(product);
            }
            productComboBox.setModel(model);
            productComboBox.setRenderer(new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                                                              boolean isSelected, boolean cellHasFocus) {
                    super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                    if (value instanceof ZentaoProduct) {
                        setText(((ZentaoProduct) value).getName());
                    }
                    return this;
                }
            });
        }

        @Override
        protected @Nullable JComponent createCenterPanel() {
            // 使用FormBuilder创建表单布局
            JPanel panel = FormBuilder.createFormBuilder()
                    .addLabeledComponent(new JBLabel("文件:"), fileChooser, true)
                    .addLabeledComponent(new JBLabel("项目:"), productComboBox, true)
                    .addComponentFillVertically(new JPanel(), 0)
                    .getPanel();

            panel.setPreferredSize(new Dimension(400, 100));
            return panel;
        }

        @Override
        protected void doOKAction() {
            ZentaoProduct selectedProduct = (ZentaoProduct) productComboBox.getSelectedItem();
            VirtualFile selectedFile = LocalFileSystem.getInstance().findFileByPath(fileChooser.getText());
            if (selectedFile != null && selectedProduct != null) {
                super.doOKAction();
                zentaoCreateTask(selectedFile, selectedProduct);

            } else {
                Messages.showErrorDialog(project, "请选择文件和项目", "错误");
            }
        }

        private void zentaoCreateTask(VirtualFile selectedFile, ZentaoProduct selectedProduct) {
            // 在后台线程中执行任务

            ProgressManager.getInstance().run(new com.intellij.openapi.progress.Task.Backgroundable(project, "导入禅道任务", true) {
                @Override
                public void run(@NotNull ProgressIndicator indicator) {
                    try {
                        // 设置进度条标题
                        indicator.setIndeterminate(false);
                        indicator.setText("正在读取Excel文件...");

                        ImportParams importParams = new ImportParams();
                        List<TaskImport> list = null;

                        // 读取Excel文件
                        list = ExcelImportUtil.importExcel(selectedFile.getInputStream(), TaskImport.class,
                                importParams);

                        if (list == null || list.isEmpty()) {
                            throw new Exception("Excel文件为空或格式不正确");
                        }

                        indicator.setText("正在处理任务...");
                        indicator.setFraction(0.0);

                        List<Task> taskList = new ArrayList<>();
                        double progress = 0.0;
                        double step = 0.5 / list.size(); // 分配50%的进度给任务处理

                        for (TaskImport taskImport : list) {
                            if (indicator.isCanceled()) {
                                return;
                            }

                            // 处理后台任务
                            if (taskImport.getBackendPersonnelAssignment() != null
                                    && !taskImport.getBackendPersonnelAssignment().equals("\\")) {
                                processBackendTasks(taskImport, taskList);
                            }

                            // 处理前端任务
                            if (taskImport.getFrontendPersonnelAssignment() != null
                                    && !taskImport.getFrontendPersonnelAssignment().equals("\\")) {
                                processFrontendTasks(taskImport, taskList);
                            }

                            progress += step;
                            indicator.setFraction(progress);
                        }

                        // 创建任务
                        indicator.setText("正在创建禅道任务...");
                        step = 0.5 / taskList.size(); // 分配剩余50%的进度给任务创建
                        progress = 0.5;

                        for (Task task : taskList) {
                            if (indicator.isCanceled()) {
                                return;
                            }

                            indicator.setText2("正在创建任务: " + task.getTitle());
                            zentaoApiService.createTask(task, selectedProduct.getId());

                            progress += step;
                            indicator.setFraction(progress);
                        }

                        // 完成后在UI线程显示成功消息
                        ApplicationManager.getApplication().invokeLater(() -> {
                            Messages.showInfoMessage(project, "成功导入 " + taskList.size() + " 个任务！", "导入成功");
                        });

                    } catch (Exception e) {
                        e.printStackTrace();
                        // 错误处理
                        ApplicationManager.getApplication().invokeLater(() -> {
                            Messages.showErrorDialog(project, "导入失败：" + e.getMessage(), "错误");
                        });
                    }
                }
            });
        }

        // 将后台任务处理逻辑提取为单独的方法
        private void processBackendTasks(TaskImport taskImport, List<Task> taskList) {
            if (taskImport.getBackendTaskBreakdown() != null) {
                List<String> backendTasks = extractNumberedTasks(taskImport.getBackendTaskBreakdown());
                double totalDuration = 0;
                try {
                    totalDuration = Double
                            .parseDouble(ObjectUtil.defaultIfNull(taskImport.getBackendTaskDuration(), "0"));
                } catch (NumberFormatException e) {
                    totalDuration = 0;
                }

                double avgDuration = backendTasks.isEmpty() ? 0 : totalDuration / backendTasks.size();

                for (String backendTask : backendTasks) {
                    Task task = new Task();
                    task.setRequirementId(taskImport.getRequirementId());

                    String duration = extractDuration(backendTask);
                    if (duration != null) {
                        task.setTitle(backendTask.replaceAll("\\s*\\d+(\\.\\d+)?h\\s*$", "").trim());
                        task.setTaskDuration(duration);
                    } else {
                        task.setTitle(backendTask);
                        task.setTaskDuration(String.format("%.1f", avgDuration));
                    }

                    task.setAssignment(taskImport.getBackendPersonnelAssignment());
                    taskList.add(task);
                }
            }
        }

        // 将前端任务处理逻辑提取为单独的方法
        private void processFrontendTasks(TaskImport taskImport, List<Task> taskList) {
            if (taskImport.getFrontendTaskBreakdown() != null) {
                List<String> frontendTasks = extractNumberedTasks(taskImport.getFrontendTaskBreakdown());
                double totalDuration = Double
                        .parseDouble(ObjectUtil.defaultIfNull(taskImport.getFrontendTaskDuration(), "0"));
                double avgDuration = frontendTasks.isEmpty() ? 0 : totalDuration / frontendTasks.size();

                for (String frontendTask : frontendTasks) {
                    Task task = new Task();
                    task.setRequirementId(taskImport.getRequirementId());

                    String duration = extractDuration(frontendTask);
                    if (duration != null) {
                        task.setTitle(frontendTask.replaceAll("\\s*\\d+(\\.\\d+)?h\\s*$", "").trim());
                        task.setTaskDuration(duration);
                    } else {
                        task.setTitle(frontendTask);
                        task.setTaskDuration(String.format("%.1f", avgDuration));
                    }

                    task.setAssignment(taskImport.getFrontendPersonnelAssignment());
                    taskList.add(task);
                }
            }
        }

        @Override
        protected @Nullable ValidationInfo doValidate() {
            if (fileChooser.getText().isEmpty()) {
                return new ValidationInfo("请选择文件", fileChooser);
            }
            if (productComboBox.getSelectedItem() == null) {
                return new ValidationInfo("请选择项目", productComboBox);
            }
            return super.doValidate();
        }

    }

    /**
     * 提取文本中以数字开头的任务
     */
    private static List<String> extractNumberedTasks(String text) {
        List<String> tasks = new ArrayList<>();
        if (text == null || text.trim().isEmpty()) {
            return tasks;
        }

        // 匹配以数字开头的行，如"1. 任务描述"，"2 任务描述"等
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "(?m)^\\s*(\\d+)[\\.、\\s]\\s*(.+?)(?=\\s*$|\\s*\\n\\s*\\d+[\\.、\\s]|\\z)",
                java.util.regex.Pattern.DOTALL);
        java.util.regex.Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            String taskNumber = matcher.group(1);
            String taskContent = matcher.group(2).trim();
            // 保留任务编号和内容
            tasks.add(taskNumber + ". " + taskContent);
        }

        return tasks;
    }

    /**
     * 从任务描述中提取时间估计
     * 匹配格式如: "任务描述 2h", "任务描述 1.5h"等
     */
    private static String extractDuration(String taskDescription) {
        if (taskDescription == null)
            return null;

        // 先尝试匹配带h的格式 (如 "2h", "1.5h")
        java.util.regex.Pattern patternWithH = java.util.regex.Pattern.compile("(\\d+(\\.\\d+)?)h\\s*$");
        java.util.regex.Matcher matcherWithH = patternWithH.matcher(taskDescription);

        if (matcherWithH.find()) {
            return matcherWithH.group(1);
        }

        // 如果没找到带h的格式，尝试匹配末尾的纯数字
        java.util.regex.Pattern patternJustNumber = java.util.regex.Pattern.compile("\\s+(\\d+(\\.\\d+)?)\\s*$");
        java.util.regex.Matcher matcherJustNumber = patternJustNumber.matcher(taskDescription);

        if (matcherJustNumber.find()) {
            return matcherJustNumber.group(1);
        }

        return null;
    }
}