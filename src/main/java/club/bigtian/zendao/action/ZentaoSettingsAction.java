package club.bigtian.zendao.action;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;

import club.bigtian.zendao.settings.ZentaoSettingsDialog;

/**
 * 禅道设置动作
 * 在Tools菜单中添加一个打开禅道设置对话框的选项
 */
public class ZentaoSettingsAction extends AnAction {

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            return;
        }

        // 创建并显示禅道设置对话框
        ZentaoSettingsDialog dialog = new ZentaoSettingsDialog(project);
        dialog.show();
    }
}