package club.bigtian.zendao.action;

import java.awt.Component;
import java.awt.MouseInfo;
import java.awt.Point;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;


import cn.hutool.core.util.StrUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.openapi.ui.popup.ListPopup;
import com.intellij.openapi.ui.popup.PopupStep;
import com.intellij.openapi.ui.popup.util.BaseListPopupStep;
import com.intellij.openapi.vcs.CommitMessageI;
import com.intellij.openapi.vcs.VcsDataKeys;
import com.intellij.ui.awt.RelativePoint;

import club.bigtian.zendao.api.ZentaoApiService;
import club.bigtian.zendao.entity.ZentaoBug;
import club.bigtian.zendao.entity.ZentaoTask;
import club.bigtian.zendao.settings.ZentaoSettings;
import club.bigtian.zendao.settings.ZentaoGlobalSettings;
import club.bigtian.zendao.settings.ZentaoSettingsDialog;

/**
 * Git提交消息模板按钮动作
 * 在提交面板上添加按钮，点击后显示提交模板选择菜单
 */
public class GitCommitTemplateAction extends AnAction {

    // 静态提交模板列表
    private static final List<CommitTemplate> STATIC_TEMPLATES = Arrays.asList(
            new CommitTemplate("build", "依赖版本", "升级或更新依赖版本"),
            new CommitTemplate("feat", "新功能", "添加了新功能"),
            new CommitTemplate("fix", "修复bug", "修复了一个bug"),
            new CommitTemplate("docs", "文档更新", "更新了文档"),
            new CommitTemplate("style", "代码格式", "调整了代码格式，不影响代码运行"),
            new CommitTemplate("refactor", "重构代码", "重构了代码，既不是新增功能，也不是修改bug"),
            new CommitTemplate("test", "测试相关", "添加或修改测试用例"),
            new CommitTemplate("chore", "构建过程", "修改了构建过程或辅助工具"));

    // 禅道API服务
    private ZentaoApiService zentaoApiService;

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            return;
        }

        ZentaoSettings projectSettings = ZentaoSettings.getInstance(project);
        ZentaoGlobalSettings globalSettings = ZentaoGlobalSettings.getInstance();
        
        // 优先使用项目级配置，如果项目级配置不存在或未配置，则使用全局配置
        boolean zentaoConfigured = (projectSettings != null && projectSettings.isConfigured()) || globalSettings.isConfigured();

        if (!zentaoConfigured) {
            openZentaoSettings(project);
            return;
        }

        // 获取提交消息面板
        CommitMessageI commitMessage = getCommitMessage(e);
        if (commitMessage == null) {
            return;
        }

        // 初始化禅道API服务
        zentaoApiService = new ZentaoApiService(project);

        // 显示模板选择弹窗
        showTemplateTypePopup(e, commitMessage);
    }

    /**
     * 显示模板类型选择弹窗
     * 
     * @param e             事件
     * @param commitMessage 提交消息面板
     */
    private void showTemplateTypePopup(AnActionEvent e, CommitMessageI commitMessage) {
        List<String> options = new ArrayList<>();

        options.add("禅道任务提交");
        options.add("禅道Bug修复提交");

        ListPopup popup = JBPopupFactory.getInstance().createListPopup(
                new BaseListPopupStep<String>("选择提交类型", options) {
                    @Override
                    public @Nullable PopupStep<?> onChosen(String selectedValue, boolean finalChoice) {
                        if (finalChoice) {
                            switch (selectedValue) {
                                case "禅道任务提交":
                                    showZentaoTasksPopup(e, commitMessage);
                                    break;
                                case "禅道Bug修复提交":
                                    showZentaoBugsPopup(e, commitMessage);
                                    break;
                            }
                            return FINAL_CHOICE;
                        }
                        return FINAL_CHOICE;
                    }
                });

        showPopupAtPosition(e, popup);
    }

    /**
     * 显示静态提交模板选择弹窗
     * 
     * @param e             事件
     * @param commitMessage 提交消息面板
     */
    private void showStaticTemplatePopup(AnActionEvent e, CommitMessageI commitMessage) {
        ListPopup popup = JBPopupFactory.getInstance().createListPopup(
                new BaseListPopupStep<CommitTemplate>(null, STATIC_TEMPLATES) {
                    @Override
                    public @NotNull String getTextFor(CommitTemplate value) {
                        return value.getType() + ": " + value.getDescription();
                    }

                    @Override
                    public @Nullable PopupStep<?> onChosen(CommitTemplate selectedValue, boolean finalChoice) {
                        if (finalChoice) {
                            // 当用户选择了一个模板，询问附加信息
                            applyStaticTemplate(commitMessage, selectedValue);
                            return FINAL_CHOICE;
                        }
                        return FINAL_CHOICE;
                    }
                });

        showPopupAtPosition(e, popup);
    }

    /**
     * 显示禅道任务选择弹窗
     *
     * @param e             事件
     * @param commitMessage 提交消息面板
     */
    private void showZentaoTasksPopup(AnActionEvent e, CommitMessageI commitMessage) {
        // 直接加载任务数据并显示弹窗
        loadTasksAndShowPopup(e, commitMessage);
    }

    /**
     * 显示禅道Bug选择弹窗
     *
     * @param e             事件
     * @param commitMessage 提交消息面板
     */
    private void showZentaoBugsPopup(AnActionEvent e, CommitMessageI commitMessage) {
        // 直接加载Bug数据并显示弹窗
        loadBugsAndShowPopup(e, commitMessage);
    }



    /**
     * 加载Bug列表并显示弹窗
     *
     * @param e             事件
     * @param commitMessage 提交消息面板
     */
    private void loadBugsAndShowPopup(AnActionEvent e, CommitMessageI commitMessage) {
        // 创建后台任务，避免UI卡顿
        ProgressManager.getInstance().run(new Task.Backgroundable(e.getProject(), "正在加载Bug列表", false) {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                indicator.setIndeterminate(true);
                indicator.setText("正在加载Bug列表...");

                final List<ZentaoBug> allBugs = new ArrayList<>();

                try {
                    // 获取产品的Bug列表
                    Collection<ZentaoBug> bugs = zentaoApiService.getMyBugs();

                    // 将产品名称添加到Bug标题前(方便用户区分)
                    for (ZentaoBug bug : bugs) {
                        bug.setTitle("[" + bug.getProductName() + "] " + bug.getTitle());
                    }

                    // 添加到总Bug列表
                    allBugs.addAll(bugs);

                } catch (Exception ex) {
                    // 处理异常，记录日志
                    Logger.getInstance(GitCommitTemplateAction.class).warn("加载Bug列表失败", ex);
                }

                // 在UI线程显示结果
                ApplicationManager.getApplication().invokeLater(() -> {
                    if (allBugs.isEmpty()) {
                        // 创建一个空列表的弹窗，显示没有Bug的信息
                        List<String> emptyMessage = Collections.singletonList("没有找到待修复的Bug");
                        ListPopup popup = JBPopupFactory.getInstance().createListPopup(
                                new BaseListPopupStep<String>(null, emptyMessage) {
                                    @Override
                                    public @NotNull String getTextFor(String value) {
                                        return value;
                                    }

                                    @Override
                                    public boolean isSelectable(String value) {
                                        return false; // 设置为不可选择
                                    }
                                });
                        showPopupAtPosition(e, popup);
                    } else {
                        // 按Bug ID排序，方便查找
                        allBugs.sort(Comparator.comparing(ZentaoBug::getId));
                        showBugListPopup(e, commitMessage, allBugs);
                    }
                });
            }
        });
    }

    /**
     * 显示Bug列表选择弹窗
     * 
     * @param e             事件
     * @param commitMessage 提交消息面板
     * @param bugs          Bug列表
     */
    private void showBugListPopup(AnActionEvent e, CommitMessageI commitMessage, List<ZentaoBug> bugs) {
        // 使用BaseListPopupStep创建支持SpeedSearch的弹窗
        ListPopup popup = JBPopupFactory.getInstance().createListPopup(
                new BaseListPopupStep<ZentaoBug>(null, bugs) {
                    @Override
                    public @NotNull String getTextFor(ZentaoBug value) {
                        return "#" + value.getId() + " " + value.getTitle();
                    }

                    @Override
                    public @Nullable PopupStep<?> onChosen(ZentaoBug selectedValue, boolean finalChoice) {
                        if (finalChoice) {
                            applyBugTemplate(commitMessage, selectedValue);
                            return FINAL_CHOICE;
                        }
                        return FINAL_CHOICE;
                    }

                    @Override
                    public boolean isSpeedSearchEnabled() {
                        return true; // 启用快速搜索功能
                    }
                });

        showPopupAtPosition(e, popup);
    }

    /**
     * 在指定位置显示弹窗
     * 
     * @param e     事件
     * @param popup 弹窗
     */
    private void showPopupAtPosition(AnActionEvent e, ListPopup popup) {
        // 获取源组件（按钮）
        Component sourceComponent = e.getInputEvent().getComponent();
        if (sourceComponent != null) {
            // 在源组件下方显示弹窗
            popup.showUnderneathOf(sourceComponent);
        } else {
            // 后备方案：获取当前鼠标位置
            Point mouseLocation = MouseInfo.getPointerInfo().getLocation();
            RelativePoint point = new RelativePoint(mouseLocation);
            popup.show(point);
        }
    }

    /**
     * 应用静态提交模板
     * 
     * @param commitMessage 提交消息面板
     * @param template      选择的模板
     */
    private void applyStaticTemplate(CommitMessageI commitMessage, CommitTemplate template) {
        // 直接构建提交消息
        String message = template.getType() + ": " + template.getDescription();

        // 设置提交消息
        applyCommitMessage(commitMessage, message);
    }

    /**
     * 应用任务提交模板
     * 
     * @param commitMessage 提交消息面板
     * @param task          选择的任务
     */
    private void applyTaskTemplate(CommitMessageI commitMessage, ZentaoTask task) {
        // 获取任务详情以获取更多信息
        ZentaoTask taskDetail = zentaoApiService.getTaskDetail(task.getId());

        // 如果获取详情失败，使用列表中的基本信息
        if (taskDetail == null) {
            taskDetail = task;
        }

        // 构建提交消息，格式：Finish Task #任务编号 Cost:任务工时h 任务标题
        String message = String.format("Finish Task #%d Cost:%.1fh %s",
                taskDetail.getId(),
                taskDetail.getEstimate() != null ? taskDetail.getEstimate() : 0.0,
                taskDetail.getName());

        // 设置提交消息
        applyCommitMessage(commitMessage, message);
    }

    /**
     * 应用Bug修复提交模板
     * 
     * @param commitMessage 提交消息面板
     * @param bug           选择的Bug
     */
    private void applyBugTemplate(CommitMessageI commitMessage, ZentaoBug bug) {
        // 获取Bug详情以获取更多信息
        ZentaoBug bugDetail = zentaoApiService.getBugDetail(bug.getId());

        // 如果获取详情失败，使用列表中的基本信息
        if (bugDetail == null) {
            bugDetail = bug;
        }

        // 构建提交消息，格式：Fix Bug #bug编号 bug标题
        String message = String.format("Fix Bug #%d %s",
                bugDetail.getId(),
                bugDetail.getTitle());

        // 设置提交消息
        applyCommitMessage(commitMessage, message);
    }

    /**
     * 应用提交消息到提交面板
     * 
     * @param commitMessage 提交消息面板
     * @param message       提交消息文本
     */
    private void applyCommitMessage(CommitMessageI commitMessage, String message) {
        commitMessage.setCommitMessage(message);
        // 设置提交消息
        // CommitMessageI接口没有直接的setText方法，需要找到正确的方式设置文本
        // if (commitMessage instanceof CommitMessage) {
        //
        //     // 如果是CommitMessage实例，直接使用setText方法
        //     ((CommitMessage) commitMessage).setText(message);
        // } else {
        //     // 对于其它实现类，尝试使用反射查找setText方法
        //     try {
        //         Method setTextMethod = commitMessage.getClass().getMethod("setText", String.class);
        //         setTextMethod.invoke(commitMessage, message);
        //     } catch (Exception ex) {
        //         // 如果反射失败，则提示用户手动复制
        //         Messages.showInfoMessage(
        //                 "已生成提交消息: " + message + "\n请手动复制到提交框中",
        //                 "提交模板");
        //     }
        // }
    }

    /**
     * 获取提交消息面板
     *
     * @param e 事件
     * @return 提交消息面板，如果找不到则返回null
     */
    @Nullable
    private CommitMessageI getCommitMessage(AnActionEvent e) {
        // 尝试从数据上下文获取CommitMessageI实例
        CommitMessageI commitMessage = e.getData(VcsDataKeys.COMMIT_MESSAGE_CONTROL);
        if (commitMessage == null) {
            // 提示找不到CommitMessage
            Messages.showErrorDialog(
                    "无法获取提交面板，请尝试在提交面板打开时使用此功能",
                    "错误");
        }
        return commitMessage;
    }

    /**
     * 显示禅道未配置的提示消息
     * 
     * @param project 项目
     */
    private void showZentaoNotConfiguredMessage(Project project) {
        Messages.showInfoMessage(
                "禅道未配置，请先在菜单 Tools->禅道设置 中配置禅道信息。",
                "禅道未配置");
    }

    /**
     * 打开禅道设置对话框
     * 
     * @param project 项目
     */
    private void openZentaoSettings(Project project) {
        // 创建并显示禅道设置对话框
        ZentaoSettingsDialog dialog = new ZentaoSettingsDialog(project);
        dialog.show();
    }

    /**
     * 加载任务列表并显示弹窗
     *
     * @param e             事件
     * @param commitMessage 提交消息面板
     */
    private void loadTasksAndShowPopup(AnActionEvent e, CommitMessageI commitMessage) {
        // 创建后台任务，避免UI卡顿
        ProgressManager.getInstance().run(new Task.Backgroundable(e.getProject(), "正在加载任务列表", false) {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                indicator.setIndeterminate(true);
                indicator.setText("正在加载任务列表...");

                final List<ZentaoTask> tasks = new ArrayList<>();

                try {
                    // 获取任务列表
                    List<ZentaoTask> allTasks = zentaoApiService.getMyTasks()
                            .stream().filter(el->StrUtil.containsAny(el.getStatus(),"wait","doing"))
                            .toList();

                    tasks.addAll(allTasks);

                } catch (Exception ex) {
                    // 处理异常，记录日志
                    Logger.getInstance(GitCommitTemplateAction.class).warn("加载任务列表失败", ex);
                }

                // 在UI线程显示结果
                ApplicationManager.getApplication().invokeLater(() -> {
                    if (tasks.isEmpty()) {
                        // 创建一个空列表的弹窗，显示没有任务的信息
                        List<String> emptyMessage = Collections.singletonList("没有找到进行中的任务");
                        ListPopup popup = JBPopupFactory.getInstance().createListPopup(
                                new BaseListPopupStep<String>(null, emptyMessage) {
                                    @Override
                                    public @NotNull String getTextFor(String value) {
                                        return value;
                                    }

                                    @Override
                                    public boolean isSelectable(String value) {
                                        return false; // 设置为不可选择
                                    }
                                });
                        showPopupAtPosition(e, popup);
                    } else {
                        showTaskListPopup(e, commitMessage, tasks);
                    }
                });
            }
        });
    }

    /**
     * 显示任务列表选择弹窗
     * 
     * @param e             事件
     * @param commitMessage 提交消息面板
     * @param tasks         任务列表
     */
    private void showTaskListPopup(AnActionEvent e, CommitMessageI commitMessage, List<ZentaoTask> tasks) {
        // 使用BaseListPopupStep创建支持SpeedSearch的弹窗
        ListPopup popup = JBPopupFactory.getInstance().createListPopup(
                new BaseListPopupStep<ZentaoTask>(null, tasks) {
                    @Override
                    public @NotNull String getTextFor(ZentaoTask value) {
                        return "#" + value.getId()+" "+value.getEstimate()+"h " + "[ " + value.getProjectName() + " ]" + value.getName();
                    }

                    @Override
                    public @Nullable PopupStep<?> onChosen(ZentaoTask selectedValue, boolean finalChoice) {
                        if (finalChoice) {
                            applyTaskTemplate(commitMessage, selectedValue);
                            return FINAL_CHOICE;
                        }
                        return FINAL_CHOICE;
                    }

                    @Override
                    public boolean isSpeedSearchEnabled() {
                        return true; // 启用快速搜索功能
                    }
                });

        showPopupAtPosition(e, popup);
    }

    /**
     * 提交模板类
     */
    private static class CommitTemplate {
        private final String type;
        private final String description;
        private final String details;

        public CommitTemplate(String type, String description, String details) {
            this.type = type;
            this.description = description;
            this.details = details;
        }

        public String getType() {
            return type;
        }

        public String getDescription() {
            return description;
        }

        public String getDetails() {
            return details;
        }

        @Override
        public String toString() {
            return type + ": " + description;
        }
    }
}