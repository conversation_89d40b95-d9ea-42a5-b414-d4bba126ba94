package club.bigtian.zendao.entity;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import lombok.Data;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Data
public class TaskImport {


    /**
     * 需求ID
     */
    @Excel(name = "需求ID", width = 15, orderNum = "0")
    private String requirementId;

    /**
     * 标题
     */
    @Excel(name = "标题", width = 25, orderNum = "1")
    private String title;

    /**
     * 后台任务拆分
     */
    @Excel(name = "后台任务拆分", width = 40, orderNum = "2")
    private String backendTaskBreakdown;

    /**
     * 脚本
     */
    @Excel(name = "脚本", width = 20, orderNum = "3")
    private String script;

    /**
     * 接口清单
     */
    @Excel(name = "接口清单", width = 30, orderNum = "4")
    private String apiInventory;

    /**
     * 后台任务时长（小时）
     */
    @Excel(name = "任务时长（小时）", width = 15, orderNum = "5", type = 10)
    private String backendTaskDuration;

    /**
     * 后台人员分配
     */
    @Excel(name = "人员分配", width = 15, orderNum = "6",fixedIndex=6)
    private String backendPersonnelAssignment;

    /**
     * 构建模块
     */
    @Excel(name = "构建模块", width = 20, orderNum = "7")
    private String buildModule;

    /**
     * 前端任务拆分
     */
    @Excel(name = "前端任务拆分", width = 40, orderNum = "8")
    private String frontendTaskBreakdown;

    /**
     * 前端任务时长(小时)
     */
    @Excel(name = "任务时长(小时)", width = 15, orderNum = "9", type = 10)
    private String frontendTaskDuration;

    /**
     * 前端人员分配
     */
    @Excel(name = "人员分配", width = 15, orderNum = "10",fixedIndex=10)
    private String frontendPersonnelAssignment;

}
