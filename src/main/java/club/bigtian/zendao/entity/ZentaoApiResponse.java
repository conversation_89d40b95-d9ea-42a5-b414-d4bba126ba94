package club.bigtian.zendao.entity;

/**
 * 禅道API响应实体类
 * 用于封装禅道API的响应结果
 *
 * @param <T> 响应数据类型
 */
public class ZentaoApiResponse<T> {

    /**
     * 响应状态
     * success - 请求成功
     * fail - 请求失败
     */
    private String status;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 错误消息
     * 当status为fail时，该字段包含错误信息
     */
    private String message;

    /**
     * 错误码
     */
    private String code;

    /**
     * 判断API请求是否成功
     *
     * @return 如果status为success返回true，否则返回false
     */
    public boolean isSuccess() {
        return "success".equals(status);
    }

    /**
     * 获取响应状态
     *
     * @return 响应状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置响应状态
     *
     * @param status 响应状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取响应数据
     *
     * @return 响应数据
     */
    public T getData() {
        return data;
    }

    /**
     * 设置响应数据
     *
     * @param data 响应数据
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置错误消息
     *
     * @param message 错误消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置错误码
     *
     * @param code 错误码
     */
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "ZentaoApiResponse{" +
                "status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}