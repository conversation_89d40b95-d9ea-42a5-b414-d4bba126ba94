package club.bigtian.zendao.entity;


import java.util.Date;

/**
 * 禅道Bug实体类
 * 用于存储禅道Bug的详细信息
 */
public class ZentaoBug {

    /**
     * Bug ID
     */
    private Integer id;

    /**
     * Bug标题
     */
    private String title;

    /**
     * Bug状态
     * active - 激活
     * resolved - 已解决
     * closed - 已关闭
     */
    private String status;

    /**
     * Bug严重程度 (1-4)
     * 1 - 致命
     * 2 - 严重
     * 3 - 一般
     * 4 - 轻微
     */
    private Integer severity;

    /**
     * Bug优先级 (1-4)
     * 1 - 最高
     * 2 - 高
     * 3 - 中
     * 4 - 低
     */
    private Integer pri;

    /**
     * Bug重现步骤
     */
    private String steps;

    /**
     * 所属产品ID
     */
    private Integer product;

    /**
     * 所属模块ID
     */
    private Integer module;

    /**
     * 创建日期
     */
    private Date openedDate;

    /**
     * 创建人账号
     */
    private String openedBy;

    /**
     * 指派给
     */
    private String assignedTo;

    /**
     * 指派日期
     */
    private Date assignedDate;

    /**
     * 解决者
     */
    private String resolvedBy;

    /**
     * 解决日期
     */
    private Date resolvedDate;

    /**
     * 解决方案
     * bydesign - 设计如此
     * duplicate - 重复Bug
     * external - 外部原因
     * fixed - 已修复
     * notrepro - 无法重现
     * postponed - 延期处理
     * willnotfix - 不予修复
     */
    private String resolution;

    /**
     * 关闭者
     */
    private String closedBy;

    /**
     * 关闭日期
     */
    private Date closedDate;

    /**
     * 最后修改日期
     */
    private Date lastEditedDate;

    /**
     * 最后修改者
     */
    private String lastEditedBy;

    /**
     * 关联的任务ID
     */
    private Integer task;

    /**
     * 相关需求ID
     */
    private Integer story;

    private String productName;

    public Date getAssignedDate() {
        return assignedDate;
    }

    public void setAssignedDate(Date assignedDate) {
        this.assignedDate = assignedDate;
    }

    public String getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(String assignedTo) {
        this.assignedTo = assignedTo;
    }

    public String getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(String closedBy) {
        this.closedBy = closedBy;
    }

    public Date getClosedDate() {
        return closedDate;
    }

    public void setClosedDate(Date closedDate) {
        this.closedDate = closedDate;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLastEditedBy() {
        return lastEditedBy;
    }

    public void setLastEditedBy(String lastEditedBy) {
        this.lastEditedBy = lastEditedBy;
    }

    public Date getLastEditedDate() {
        return lastEditedDate;
    }

    public void setLastEditedDate(Date lastEditedDate) {
        this.lastEditedDate = lastEditedDate;
    }

    public Integer getModule() {
        return module;
    }

    public void setModule(Integer module) {
        this.module = module;
    }

    public String getOpenedBy() {
        return openedBy;
    }

    public void setOpenedBy(String openedBy) {
        this.openedBy = openedBy;
    }

    public Date getOpenedDate() {
        return openedDate;
    }

    public void setOpenedDate(Date openedDate) {
        this.openedDate = openedDate;
    }

    public Integer getPri() {
        return pri;
    }

    public void setPri(Integer pri) {
        this.pri = pri;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public String getResolvedBy() {
        return resolvedBy;
    }

    public void setResolvedBy(String resolvedBy) {
        this.resolvedBy = resolvedBy;
    }

    public Date getResolvedDate() {
        return resolvedDate;
    }

    public void setResolvedDate(Date resolvedDate) {
        this.resolvedDate = resolvedDate;
    }

    public Integer getSeverity() {
        return severity;
    }

    public void setSeverity(Integer severity) {
        this.severity = severity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSteps() {
        return steps;
    }

    public void setSteps(String steps) {
        this.steps = steps;
    }

    public Integer getStory() {
        return story;
    }

    public void setStory(Integer story) {
        this.story = story;
    }

    public Integer getTask() {
        return task;
    }

    public void setTask(Integer task) {
        this.task = task;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}