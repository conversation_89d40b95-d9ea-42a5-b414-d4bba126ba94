package club.bigtian.zendao.entity;

import java.util.Date;

/**
 * 禅道产品实体类
 * 用于存储禅道产品的详细信息
 */
public class ZentaoProduct {

    /**
     * 产品ID
     */
    private Integer id;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品代号
     */
    private String code;

    /**
     * 所属产品线ID
     */
    private Integer line;

    /**
     * 产品状态
     * normal - 正常
     * closed - 已关闭
     */
    private String status;

    /**
     * 产品负责人
     */
    private String PO;

    /**
     * 发布负责人
     */
    private String RD;

    /**
     * 测试负责人
     */
    private String QD;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 创建日期
     */
    private Date createdDate;

    /**
     * 产品描述
     */
    private String desc;

    /**
     * 产品类型
     * normal - 正常产品
     * branch - 分支产品
     */
    private String type;

    /**
     * 是否已关闭
     */
    private Boolean closed;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getLine() {
        return line;
    }

    public void setLine(Integer line) {
        this.line = line;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPO() {
        return PO;
    }

    public void setPO(String PO) {
        this.PO = PO;
    }

    public String getRD() {
        return RD;
    }

    public void setRD(String RD) {
        this.RD = RD;
    }

    public String getQD() {
        return QD;
    }

    public void setQD(String QD) {
        this.QD = QD;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getClosed() {
        return closed;
    }

    public void setClosed(Boolean closed) {
        this.closed = closed;
    }

    @Override
    public String toString() {
        return "ZentaoProduct{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}