package club.bigtian.zendao.entity;

import java.util.Date;

/**
 * 禅道API令牌实体类
 * 用于存储API认证令牌及相关信息
 */
public class ZentaoToken {

    /**
     * 令牌字符串
     */
    private String token;

    /**
     * 令牌过期时间
     */
    private Date expireTime;

    /**
     * 令牌类型
     */
    private String tokenType;

    /**
     * 用户ID
     */
    private Integer userId;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 检查令牌是否有效
     *
     * @return 如果令牌存在且未过期返回true，否则返回false
     */
    public boolean isValid() {
        if (token == null || token.isEmpty()) {
            return false;
        }

        if (expireTime == null) {
            return true; // 如果没有过期时间，假定令牌有效
        }

        return expireTime.after(new Date());
    }
}