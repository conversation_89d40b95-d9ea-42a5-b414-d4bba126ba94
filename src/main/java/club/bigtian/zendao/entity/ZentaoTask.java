package club.bigtian.zendao.entity;

import java.util.Date;

/**
 * 禅道任务实体类
 * 用于存储禅道任务的详细信息
 */
public class ZentaoTask {

    /**
     * 任务ID
     */
    private Integer id;

    /**
     * 任务名称/标题
     */
    private String name;

    /**
     * 任务状态
     * wait - 未开始
     * doing - 进行中
     * done - 已完成
     * pause - 已暂停
     * cancel - 已取消
     * closed - 已关闭
     */
    private String status;

    /**
     * 任务优先级 (1-4)
     * 1 - 最高
     * 2 - 高
     * 3 - 中
     * 4 - 低
     */
    private Integer pri;

    /**
     * 预计工时
     */
    private Double estimate;

    /**
     * 已消耗工时
     */
    private Double consumed;

    /**
     * 剩余工时
     */
    private Double left;

    /**
     * 截止日期
     */
    private Date deadline;

    /**
     * 任务描述
     */
    private String desc;

    /**
     * 创建人账号
     */
    private String openedBy;

    /**
     * 指派给
     */
    private String assignedTo;

    /**
     * 所属项目ID
     */
    private Integer project;

    /**
     * 所属模块ID
     */
    private Integer module;

    /**
     * 创建日期
     */
    private Date openedDate;

    /**
     * 最后修改日期
     */
    private Date lastEditedDate;

    /**
     * 完成者
     */
    private String finishedBy;

    /**
     * 完成日期
     */
    private Date finishedDate;

    /**
     * 关闭者
     */
    private String closedBy;

    /**
     * 关闭日期
     */
    private Date closedDate;

    /**
     * 关闭原因
     */
    private String closedReason;
    private String projectName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPri() {
        return pri;
    }

    public void setPri(Integer pri) {
        this.pri = pri;
    }

    public Double getEstimate() {
        return estimate;
    }

    public void setEstimate(Double estimate) {
        this.estimate = estimate;
    }

    public Double getConsumed() {
        return consumed;
    }

    public void setConsumed(Double consumed) {
        this.consumed = consumed;
    }

    public Double getLeft() {
        return left;
    }

    public void setLeft(Double left) {
        this.left = left;
    }

    public Date getDeadline() {
        return deadline;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getOpenedBy() {
        return openedBy;
    }

    public void setOpenedBy(String openedBy) {
        this.openedBy = openedBy;
    }

    public String getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(String assignedTo) {
        this.assignedTo = assignedTo;
    }

    public Integer getProject() {
        return project;
    }

    public void setProject(Integer project) {
        this.project = project;
    }

    public Integer getModule() {
        return module;
    }

    public void setModule(Integer module) {
        this.module = module;
    }

    public Date getOpenedDate() {
        return openedDate;
    }

    public void setOpenedDate(Date openedDate) {
        this.openedDate = openedDate;
    }

    public Date getLastEditedDate() {
        return lastEditedDate;
    }

    public void setLastEditedDate(Date lastEditedDate) {
        this.lastEditedDate = lastEditedDate;
    }

    public String getFinishedBy() {
        return finishedBy;
    }

    public void setFinishedBy(String finishedBy) {
        this.finishedBy = finishedBy;
    }

    public Date getFinishedDate() {
        return finishedDate;
    }

    public void setFinishedDate(Date finishedDate) {
        this.finishedDate = finishedDate;
    }

    public String getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(String closedBy) {
        this.closedBy = closedBy;
    }

    public Date getClosedDate() {
        return closedDate;
    }

    public void setClosedDate(Date closedDate) {
        this.closedDate = closedDate;
    }

    public String getClosedReason() {
        return closedReason;
    }

    public void setClosedReason(String closedReason) {
        this.closedReason = closedReason;
    }

    @Override
    public String toString() {
        return "ZentaoTask{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status='" + status + '\'' +
                ", assignedTo='" + assignedTo + '\'' +
                '}';
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
}