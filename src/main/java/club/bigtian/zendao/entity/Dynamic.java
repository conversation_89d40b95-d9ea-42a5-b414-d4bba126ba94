package club.bigtian.zendao.entity;

import lombok.Data;

/**
 * 禅道动态实体类
 */
@Data
public class Dynamic {
    /**
     * 动态ID
     */
    private Integer id;

    /**
     * 动态类型
     */
    private String type;

    /**
     * 动态动作
     */
    private String action;

    /**
     * 动态日期
     */
    private String date;

    /**
     * 对象类型
     */
    private String objectType;

    /**
     * 对象ID
     */
    private Integer objectId;

    /**
     * 操作者
     */
    private String actor;

    /**
     * 评论或对象名称
     */
    private String comment;

    /**
     * 额外信息
     */
    private String extra;
}