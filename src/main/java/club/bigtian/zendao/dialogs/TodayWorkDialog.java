package club.bigtian.zendao.dialogs;

import java.awt.Component;
import java.awt.Toolkit;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.List;

import javax.swing.DefaultListModel;
import javax.swing.Icon;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JList;
import javax.swing.JPanel;
import javax.swing.ListCellRenderer;

import org.jetbrains.annotations.NotNull;

import com.intellij.icons.AllIcons;
import com.intellij.notification.NotificationGroupManager;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.util.ui.JBUI;

import club.bigtian.zendao.api.ZentaoApiService;
import club.bigtian.zendao.entity.Dynamic;
import cn.hutool.core.collection.CollUtil;

public class TodayWorkDialog extends JDialog {
    private JPanel contentPane;
    private JButton buttonOK;
    private JButton buttonCancel;
    private com.intellij.openapi.ui.FixedSizeButton copy;
    private JList<ListItem> list;
    private ZentaoApiService zentaoApiService;
    private Project project;

    // 自定义列表项类
    private static class ListItem {
        String objectType;
        String text;

        ListItem(String objectType, String text) {
            this.objectType = objectType;
            this.text = text;
        }

        @Override
        public String toString() {
            return text;
        }
    }

    // 自定义单元格渲染器
    private static class CustomListCellRenderer extends JLabel implements ListCellRenderer<ListItem> {
        private final Icon bugIcon;
        private final Icon taskIcon;
        private static final int MAX_DISPLAY_LENGTH = 45; // 最大显示字符数

        public CustomListCellRenderer() {
            setOpaque(true);
            // 使用IntelliJ内置图标
            bugIcon = AllIcons.General.BalloonError;
            taskIcon = AllIcons.Actions.Execute;
            setBorder(JBUI.Borders.empty(2, 4));
        }

        @Override
        public Component getListCellRendererComponent(JList<? extends ListItem> list, ListItem value,
                int index, boolean isSelected, boolean cellHasFocus) {
            // 动态计算合适的显示长度
            int dynamicMaxLength = calculateMaxDisplayLength(list);
            String displayText = truncateText(value.text, dynamicMaxLength);
            setText(displayText);
            setIcon("bug".equalsIgnoreCase(value.objectType) ? bugIcon : taskIcon);

            if (isSelected) {
                setBackground(list.getSelectionBackground());
                setForeground(list.getSelectionForeground());
            } else {
                setBackground(list.getBackground());
                setForeground(list.getForeground());
            }

            return this;
        }

        /**
         * 根据列表宽度动态计算最大显示字符数
         */
        private int calculateMaxDisplayLength(JList<? extends ListItem> list) {
            int listWidth = list.getWidth();
            if (listWidth <= 0) {
                return MAX_DISPLAY_LENGTH; // 如果无法获取宽度，使用默认值
            }

            // 减去图标宽度(16px)、边距(8px)、滚动条宽度(20px)等
            int availableWidth = listWidth - 70;

            // 假设平均每个字符占用12像素（考虑中英文混合）
            int maxChars = availableWidth / 12;

            // 确保最小值和最大值
            return Math.max(20, Math.min(maxChars, MAX_DISPLAY_LENGTH));
        }

        /**
         * 截断文本，如果超过最大长度则添加省略号
         */
        private String truncateText(String text, int maxLength) {
            if (text == null || text.length() <= maxLength) {
                return text;
            }
            return text.substring(0, maxLength - 3) + "...";
        }
    }

    public static void show(Project project) {
        ProgressManager.getInstance().run(new Task.Backgroundable(project, "加载今日动态...", false) {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                indicator.setIndeterminate(true);

                // 创建对话框实例但不显示
                TodayWorkDialog dialog = new TodayWorkDialog(project);
                List<Dynamic> dynamicList = dialog.zentaoApiService.getTodayDynamic();


                // 在EDT中更新UI并显示对话框
                List<Dynamic> finalDynamicList = dynamicList;
                javax.swing.SwingUtilities.invokeLater(() -> {
                    dialog.updateListModel(finalDynamicList);
                    dialog.setVisible(true);
                });
            }
        });
    }

    private TodayWorkDialog(Project project) {
        this.project = project;
        zentaoApiService = new ZentaoApiService(project);
        setContentPane(contentPane);
        setModal(true);
        getRootPane().setDefaultButton(buttonOK);
        setTitle("今日动态");

        // 设置对话框大小和位置
        setSize(600, 400);
        setLocationRelativeTo(null); // 居中显示

        buttonOK.addActionListener(e -> onOK());
        buttonCancel.addActionListener(e -> onCancel());
        copy.addActionListener(e -> copySelectedContent());

        // call onCancel() when cross is clicked
        setDefaultCloseOperation(DO_NOTHING_ON_CLOSE);
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                onCancel();
            }
        });
    }

    /**
     * 更新列表模型
     */
    private void updateListModel(List<Dynamic> dynamicList) {
        DefaultListModel<ListItem> listModel = new DefaultListModel<>();
        for (Dynamic dynamic : dynamicList) {
            String prefix = "";
            String actionText = "";

            // 根据objectType判断是bug还是任务
            if ("bug".equalsIgnoreCase(dynamic.getObjectType())) {
                prefix = "[Bug] ";
            } else if ("task".equalsIgnoreCase(dynamic.getObjectType())) {
                prefix = "[任务] ";
            }

            // 根据action构建动作文本
            String action = dynamic.getAction();
            if ("finished".equalsIgnoreCase(action)) {
                actionText = "完成了 ";
            } else if ("resolved".equalsIgnoreCase(action)) {
                actionText = "解决了 ";
            } else if ("opened".equalsIgnoreCase(action)) {
                actionText = "创建了 ";
            } else if ("commented".equalsIgnoreCase(action)) {
                actionText = "评论了 ";
            } else if ("started".equalsIgnoreCase(action)) {
                actionText = "开始了 ";
            } else {
                actionText = action + " "; // 其他未知action类型
            }

            // 只显示comment内容
            String comment = dynamic.getComment();
            if (comment != null && !comment.trim().isEmpty()) {
                listModel.addElement(new ListItem(dynamic.getObjectType(), prefix + actionText + comment));
            }
        }

        // 设置JList的数据模型和单元格渲染器
        list.setModel(listModel);
        list.setCellRenderer(new CustomListCellRenderer());
    }

    private void onOK() {
        dispose();
    }

    private void onCancel() {
        dispose();
    }

    /**
     * 复制选中内容到剪贴板
     */
    private void copySelectedContent() {
        DefaultListModel<ListItem> model = (DefaultListModel<ListItem>) list.getModel();
        if (model.isEmpty()) {
            Messages.showWarningDialog("没有可复制的内容", "提示");
            return;
        }

        // 构建所有内容，去掉[Bug]和[任务]前缀，添加序号
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < model.getSize(); i++) {
            ListItem item = model.getElementAt(i);
            String text = item.text
                    .replace("[Bug] ", "")
                    .replace("[任务] ", "");
            content.append(i + 1).append(". ").append(text).append("\n");
        }

        // 创建StringSelection对象
        StringSelection selection = new StringSelection(content.toString().trim());

        // 获取系统剪贴板
        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();

        // 将文本复制到剪贴板
        clipboard.setContents(selection, null);

        // 显示右下角通知
        NotificationGroupManager.getInstance()
                .getNotificationGroup("Zentao Notification Group")
                .createNotification("复制成功", NotificationType.INFORMATION)
                .notify(project);
    }
}
