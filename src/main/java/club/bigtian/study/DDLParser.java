package club.bigtian.study;

import club.bigtian.dto.ColumnDTO;
import club.bigtian.dto.TableDTO;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

import java.util.*;

public class DDLParser {


    /**
     * 解析ddl语句，获取字段名、字段类型和字段注释
     *
     * @return
     */
    public static TableDTO parseDDLFields(String ddl) {
        ddl = ddl.replaceAll("\n", "").replaceAll("`", "");
        //正则获取表名
        String tableName = ObjectUtil.defaultIfNull(ReUtil.get("create table (\\w+)", ddl, 1), ReUtil.get("CREATE TABLE (\\w+)", ddl, 1));
        String tableComment = ReUtil.get("\\)\s*comment '(.*?)'", ddl, 1);


        ddl = ObjectUtil.defaultIfNull(ReUtil.get("create table " + tableName + "\s*\\((.*)\\)", ddl, 1), ReUtil.get("CREATE TABLE " + tableName + "\s*\\((.*)\\)", ddl, 1));

        TableDTO tableDTO = new TableDTO();
        tableDTO.setTableComment(tableComment);
        tableDTO.setTableName(tableName);
        //把每行的ddl语句分割成数组
        String[] split = ddl.replaceAll("\\((.*?)\\)", "").split(",");
        ArrayList<ColumnDTO> list = new ArrayList<>();
        HashSet<String> packageSet = new HashSet<>();
        for (String s : split) {
            String result = s.trim().replaceAll("\\s+", ",");

            if (result.startsWith("primary") || result.startsWith("PRIMARY")) {
                String primaryKey = ReUtil.getGroup1("\\((.*?)\\)", s);
                list.stream().filter(el -> el.getFieldName().equalsIgnoreCase(primaryKey)).findFirst().get().setPrimaryKey(true);
                continue;
            }
            ColumnDTO columnDTO = new ColumnDTO();
            boolean primaryKey = s.contains("primary key") || s.contains("PRIMARY KEY");
            columnDTO.setPrimaryKey(primaryKey);
            int idx = result.indexOf(",");
            String name = result.substring(0, idx);
            result = result.substring(idx + 1);
            idx = result.indexOf(",");

            String type = result.substring(0, idx < 0 ? result.length() : idx).replaceAll("\\(\\d+\\)", "");
            idx = !result.contains("comment") ? result.indexOf("COMMENT") : result.indexOf("comment");
            String comment = null;

            if (idx > 0) {
                result = result.substring(idx);
                comment = result.split(",")[1].replaceAll("'", "");
            }

            String fieldType = mapFieldType(type);
            if (StrUtil.isEmpty(fieldType)) {
                continue;
            }
            if (fieldType.indexOf(":") != -1) {
                packageSet.add(fieldType.split(":")[1]);
                fieldType = fieldType.split(":")[0];
            }
            columnDTO.setFieldName(name);
            columnDTO.setFieldType(fieldType);
            columnDTO.setFieldComment(comment);
           list.add(columnDTO);
        }
        tableDTO.setColumnList(list);
        tableDTO.setPackageSet(packageSet);
        return tableDTO;
    }

    private static String mapFieldType(String fieldType) {
        fieldType = fieldType.toUpperCase();
        String javaType = null;
        String packagePath = null;

        // 自定义字段类型映射
        if (fieldType.equalsIgnoreCase("DECIMAL")) {
            javaType = "BigDecimal";
            packagePath = "java.math";
        } else if (fieldType.equalsIgnoreCase("TINYINT")) {
            javaType = "byte";
        } else if (fieldType.equalsIgnoreCase("SMALLINT")) {
            javaType = "short";
        } else if (fieldType.equalsIgnoreCase("MEDIUMINT") ||
                fieldType.equalsIgnoreCase("INT")) {
            javaType = "int";
        } else if (fieldType.equalsIgnoreCase("BIGINT")) {
            javaType = "long";
        } else if (fieldType.equalsIgnoreCase("FLOAT")) {
            javaType = "float";
        } else if (fieldType.equalsIgnoreCase("DOUBLE")) {
            javaType = "double";
        } else if (fieldType.equalsIgnoreCase("DATE") ||
                fieldType.equalsIgnoreCase("TIME") ||
                fieldType.equalsIgnoreCase("DATETIME") ||
                fieldType.equalsIgnoreCase("TIMESTAMP") ||
                fieldType.equalsIgnoreCase("YEAR")) {
            javaType = "Date";
            packagePath = "java.util";
        } else if (fieldType.equalsIgnoreCase("CHAR") ||
                fieldType.equalsIgnoreCase("VARCHAR") ||
                fieldType.equalsIgnoreCase("TINYTEXT") ||
                fieldType.equalsIgnoreCase("TEXT") ||
                fieldType.equalsIgnoreCase("MEDIUMTEXT") ||
                fieldType.equalsIgnoreCase("LONGTEXT") ||
                fieldType.equalsIgnoreCase("ENUM") ||
                fieldType.equalsIgnoreCase("SET") ||
                fieldType.equalsIgnoreCase("JSON")) {
            javaType = "String";
        } else if (fieldType.equalsIgnoreCase("BINARY") ||
                fieldType.equalsIgnoreCase("VARBINARY") ||
                fieldType.equalsIgnoreCase("TINYBLOB") ||
                fieldType.equalsIgnoreCase("BLOB") ||
                fieldType.equalsIgnoreCase("MEDIUMBLOB") ||
                fieldType.equalsIgnoreCase("LONGBLOB")) {
            javaType = "byte[]";
        } else if (fieldType.equalsIgnoreCase("BOOLEAN") || fieldType.equals("BOOL")) {
            javaType = "boolean";
        }

        if (javaType != null) {
            // 设置包路径
            if (packagePath == null) {
                return javaType;
            }
            return javaType + ":" + packagePath;
        }

        // 其他类型保持不变
        return null;
    }

}


