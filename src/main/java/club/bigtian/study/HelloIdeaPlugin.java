package club.bigtian.study;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.util.Consumer;

import java.util.Arrays;
import java.util.List;

public class HelloIdeaPlugin extends AnAction {

    @Override
    public void actionPerformed(AnActionEvent e) {
        String[] data = new String[]{"Java", "C#", "C++"};
        List<String> list = Arrays.asList(data);
        JBPopupFactory instance = JBPopupFactory.getInstance();// 创建实例
        instance.createPopupChooserBuilder(list)
                .setTitle("Which One Do Ypu Like")// 设置标题
                .setItemChosenCallback(new Consumer<String>() {// 添加监听事件
                    @Override
                    public void consume(String s) {
                        System.out.println("s = " + s);
                    }
                }).createPopup().showInFocusCenter();// 在屏幕中间显示

    }
}
