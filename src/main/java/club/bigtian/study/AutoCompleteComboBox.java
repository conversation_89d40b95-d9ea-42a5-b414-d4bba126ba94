package club.bigtian.study;

import javax.swing.*;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.util.ArrayList;
import java.util.List;

public class AutoCompleteComboBox extends JComboBox<String> {

    private final JTextField textField;
    private final List<String> items;
    private boolean isFired = false;

    public AutoCompleteComboBox(final ComboBoxModel<String> model) {
        super(model);
        this.items = new ArrayList<>();
        for (int i = 0; i < model.getSize(); i++) {
            this.items.add((String) model.getElementAt(i));
        }
        this.textField = (JTextField) this.getEditor().getEditorComponent();
        this.textField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                String text = textField.getText();
                if (!isFired) {
                    isFired = true;
                    for (int i = 0; i < items.size(); i++) {
                        String item = items.get(i);
                        if (item.toLowerCase().startsWith(text.toLowerCase())) {
                            setSelectedIndex(i);
                            textField.setText(item);
                            textField.setSelectionStart(text.length());
                            textField.setSelectionEnd(item.length());
                            break;
                        }
                    }
                    isFired = false;
                }
            }
        });
    }
}