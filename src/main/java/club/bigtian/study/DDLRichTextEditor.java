
package club.bigtian.study;

import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.editor.EditorSettings;
import com.intellij.openapi.editor.ex.EditorEx;
import com.intellij.openapi.editor.highlighter.EditorHighlighterFactory;
import com.intellij.openapi.editor.impl.DocumentImpl;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.project.Project;


public class DDLRichTextEditor {
    private final Project project;
    private final Editor editor;


    public DDLRichTextEditor(Project project) {
        this.project = project;
        this.editor = createEditor();
    }

    private Editor createEditor() {
        EditorFactory editorFactory = EditorFactory.getInstance();
        FileTypeManager fileTypeManager = FileTypeManager.getInstance();

        // Create a new document
        DocumentImpl document = new DocumentImpl("");
        // Create a new editor
        EditorEx editor = (EditorEx) editorFactory.createEditor(document, project);

        // Set the editor settings
        EditorSettings editorSettings = editor.getSettings();
        editorSettings.setLineNumbersShown(true);
        editorSettings.setFoldingOutlineShown(true);
        editorSettings.setAdditionalColumnsCount(2);
        editorSettings.setAdditionalLinesCount(2);
        editorSettings.setRightMarginShown(true);
        editorSettings.setLeadingWhitespaceShown(true);

        editorSettings.setUseSoftWraps(true);
        editorSettings.setLineMarkerAreaShown(true);
        editorSettings.setIndentGuidesShown(true);
        editorSettings.setAutoCodeFoldingEnabled(true);

        // Set the editor highlighter
        FileType fileType = fileTypeManager.getFileTypeByExtension("SQL");
        editor.setHighlighter(EditorHighlighterFactory.getInstance().createEditorHighlighter(project, fileType));
        // Add the editor to the project
        return editor;
    }


    /**
     * 生成一个可以java代码提示的编辑器弹框
     */


    public Editor getEditor() {
        return editor;
    }


    public void dispose() {
        EditorFactory editorFactory = EditorFactory.getInstance();
        editorFactory.releaseEditor(editor);
    }
} 
 