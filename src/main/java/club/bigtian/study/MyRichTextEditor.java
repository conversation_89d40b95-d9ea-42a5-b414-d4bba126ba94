
package club.bigtian.study;

import com.intellij.codeInsight.completion.CodeCompletionHandlerBase;
import com.intellij.codeInsight.completion.CompletionType;
import com.intellij.openapi.actionSystem.IdeActions;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.editor.EditorSettings;
import com.intellij.openapi.editor.ex.DocumentEx;
import com.intellij.openapi.editor.ex.EditorEx;
import com.intellij.openapi.editor.highlighter.EditorHighlighterFactory;
import com.intellij.openapi.editor.impl.DocumentImpl;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiDocumentManager;

import javax.swing.*;
import java.awt.event.ActionEvent;


public class MyRichTextEditor {
    private final Project project;
    private final Editor editor;


    public MyRichTextEditor(Project project, String javaCode) {
        this.project = project;
        this.editor = createEditor(javaCode);
    }

    private Editor createEditor(String javaCode) {
        EditorFactory editorFactory = EditorFactory.getInstance();
        FileTypeManager fileTypeManager = FileTypeManager.getInstance();

        // Create a new document
        DocumentImpl document = new DocumentImpl("");
        // Create a new editor
        EditorEx editor = (EditorEx) editorFactory.createEditor(document, project);

        // Set the editor settings
        EditorSettings editorSettings = editor.getSettings();
        editorSettings.setLineNumbersShown(true);
        editorSettings.setFoldingOutlineShown(true);
        editorSettings.setAdditionalColumnsCount(2);
        editorSettings.setAdditionalLinesCount(2);
        editorSettings.setRightMarginShown(true);
        editorSettings.setLeadingWhitespaceShown(true);

        editorSettings.setUseSoftWraps(true);
        editorSettings.setLineMarkerAreaShown(true);
        editorSettings.setIndentGuidesShown(true);
        editorSettings.setAutoCodeFoldingEnabled(true);

        // Set the editor highlighter
        FileType fileType = fileTypeManager.getFileTypeByExtension("java");
        editor.setHighlighter(EditorHighlighterFactory.getInstance().createEditorHighlighter(project, fileType));


        WriteCommandAction.runWriteCommandAction(project, () -> {
            DocumentEx document1 = editor.getDocument();
            document1.insertString(0, javaCode);
        });

        // 设置代码补全提供者
        PsiDocumentManager psiDocumentManager = PsiDocumentManager.getInstance(project);
        psiDocumentManager.commitAllDocuments(); // 提交文档以确保代码补全提供者可用

        // 创建一个用于绑定动作的Action对象
        Action action= new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                CodeCompletionHandlerBase completionHandler = new CodeCompletionHandlerBase(
                        CompletionType.BASIC, true, true, true);
                System.out.println("进来");
                completionHandler.invokeCompletion(project, editor);
            }
        };

        // 将代码补全动作绑定到编辑器
        editor.getComponent().getActionMap().put(IdeActions.ACTION_CODE_COMPLETION, action);

        ApplicationManager.getApplication().invokeLater(() -> {
            // 获取当前选定的Editor
            if (editor != null) {
                // 将焦点设置回Editor
                editor.getContentComponent().requestFocus();
                // 如果需要，可以将光标移动到指定位置
                editor.getCaretModel().moveToOffset(0);
            }
        });
        // Add the editor to the project
        return editor;
    }

    /**
     * 生成一个可以java代码提示的编辑器弹框
     */


    public Editor getEditor() {
        return editor;
    }

    public void dispose() {
        EditorFactory editorFactory = EditorFactory.getInstance();
        editorFactory.releaseEditor(editor);
    }
} 
 