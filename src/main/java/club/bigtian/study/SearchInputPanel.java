package club.bigtian.study;

import com.intellij.openapi.ui.ComboBox;
import com.intellij.ui.CollectionComboBoxModel;
import com.intellij.ui.components.JBTextField;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import java.awt.*;
import java.util.Arrays;
import java.util.List;

public class SearchInputPanel extends JPanel {
    private JComboBox<String> searchComboBox;
    private JBTextField searchTextField;

    public SearchInputPanel() {
        initializeComponents();
    }

    private void initializeComponents() {
        setLayout(new FlowLayout());

        // 搜索条件下拉框
        List<String> searchOptions = Arrays.asList("角色", "部门", "人员");
        searchComboBox = new ComboBox<>(new CollectionComboBoxModel<>(searchOptions));
        add(searchComboBox);

        // 搜索文本输入框
        searchTextField = new JBTextField(20);
        add(searchTextField);

        // 监听输入框的文本变化事件
        searchTextField.getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                performSearch();
            }

            @Override
            public void removeUpdate(DocumentEvent e) {
                performSearch();
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                performSearch();
            }
        });
    }

    private void performSearch() {
        String searchOption = (String) searchComboBox.getSelectedItem();
        String searchText = searchTextField.getText();

        // 执行搜索操作
        // 在此处根据搜索选项和搜索文本执行相应的搜索逻辑

        System.out.println("Search Option: " + searchOption);
        System.out.println("Search Text: " + searchText);
    }

    public static void main(String[] args) {
        JFrame frame = new JFrame("Search Input Example");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        SearchInputPanel panel = new SearchInputPanel();
        frame.getContentPane().add(panel);

        frame.pack();
        frame.setVisible(true);
    }
}
