package club.bigtian.persistent;

import com.intellij.openapi.components.*;
import org.jetbrains.annotations.NotNull;

/**
 * 持久化配置
 */
@Service
@State(
        name = "PluginSettings",
        storages = {
                @Storage("pluginSettings.xml")
        }
)
public final class PluginSettings implements PersistentStateComponent<PluginSettings.State> {

    private State myState = new State();

    public static PluginSettings getInstance() {
        return ServiceManager.getService(PluginSettings.class);
    }

    @Override
    public State getState() {
        return myState;
    }

    @Override
    public void loadState(@NotNull State state) {
        myState = state;
    }


    public static class State {
        /**
         * ddl配置
         */
        public String pluginSettingJson = "";
        /**
         * workFlow常量类配置
         */
        public String pluginWorkFlowJson = "";

        /**
         * 流程平台暂存数据
         */
        public String pluginWorkFlowData = "";
        /**
         * 国际租户
         */
        public String pluginWorkInterTenant = "[\"HUN\",\"SGP\",\"AU\",\"UK\",\"KR\",\"GER\",\"BSF\",\"RUS\",\"USA\",\"IND\",\"IHK\"]";

        /**
         * 流程环境配置
         */
        public String flowEnvironments = "[{\"name\":\"公司环境\",\"env\":\"dev\",\"url\":\"https://117.78.50.82:18081/ebpm-process-manage\",\"username\":\"admin\",\"password\":\"000000\"},{\"name\":\"31环境\",\"env\":\"31\",\"url\":\"https://oaa.vazyme.com:9080/ebpm-process-manage/\",\"username\":\"admin\",\"password\":\"000000\"},{\"name\":\"正式环境\",\"env\":\"prod\",\"url\":\"https://crm.vazyme.com/ebpm-process-manage/\",\"username\":\"admin\",\"password\":\"000000\",\"gateWayAccount\":\"luob\",\"gateWayPwd\":\"4O7Xc0\"}]";
        
        /**
         * 当前选择的流程环境
         */
        public String currentFlowEnvironment = "公司环境";
    }
}
