package club.bigtian.render;

import club.bigtian.dto.BoxItem;
import com.intellij.icons.AllIcons;

import javax.swing.*;
import java.awt.*;

public class MyComboBoxRenderer extends J<PERSON>abel implements ListCellRenderer {
    // public class MyComboBoxRenderer extends <PERSON><PERSON>abel implements ListCellRenderer<BoxItem> {
    public MyComboBoxRenderer() {
        setOpaque(true);
    }

    @Override
    public Component getListCellRendererComponent(JList jList, Object o, int i, boolean b, boolean b1) {
        JPanel jPanel = new JPanel();
        BoxItem boxItem = (BoxItem) o;
        jPanel.setLayout(new BorderLayout());
        JLabel jLabel = new JLabel(boxItem.getName());
        jPanel.add(new JLabel(AllIcons.Nodes.Class), BorderLayout.WEST);
        jPanel.add(jLabel, BorderLayout.CENTER);
        setText(boxItem.getName());
        setIcon(AllIcons.Nodes.Class);
        return jPanel;
    }

    // @Override
    // public Component getListCellRendererComponent(JList<? extends BoxItem> list, BoxItem value, int index, boolean isSelected, boolean cellHasFocus) {
    //     setText(value.getName());
    //     setIcon(AllIcons.Nodes.Class);
    //     return this;
    // }
}
