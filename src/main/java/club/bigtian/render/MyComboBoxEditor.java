package club.bigtian.render;

import club.bigtian.dto.BoxItem;
import com.intellij.openapi.ui.ComboBox;

import javax.swing.*;
import java.awt.*;

public class MyComboBoxEditor extends DefaultCellEditor {
    private ComboBox comboBox;

    public MyComboBoxEditor(ComboBox comboBox) {
        super(comboBox);
        this.comboBox = comboBox;
    }

    public Object getCellEditorValue() {
        Object selectedValue = comboBox.getSelectedItem();
        if (selectedValue instanceof BoxItem) {
            return ((BoxItem) selectedValue).getName();
        } else {
            return super.getCellEditorValue();
        }
    }

    @Override
    public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
        comboBox.setSelectedItem(value);
        return comboBox;
    }
}