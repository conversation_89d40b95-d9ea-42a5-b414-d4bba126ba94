package club.bigtian.handler;

import cn.afterturn.easypoi.handler.inter.IExcelDictHandler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExcelDicHandler implements IExcelDictHandler {
    public static final String TYPE = "type";
    public static final String LANG = "lang";

    @Override
    public List<Map> getList(String dict) {
        if (TYPE.equals(dict)) {
            return type();
        } else if (LANG.equals(dict)) {
            return lang();
        }

        throw new RuntimeException("字典名称不正确");
    }

    public List<Map> lang() {
        List<Map> list = new ArrayList<>();
        Map<String, String> dictMap = new HashMap<>();

        dictMap.put("dictKey", "en");
        dictMap.put("dictValue", "英语");
        list.add(dictMap);
        dictMap = new HashMap<>();

        dictMap.put("dictKey", "zh");
        dictMap.put("dictValue", "中文");
        list.add(dictMap);
        return list;
    }

    public List<Map> type() {
        List<Map> list = new ArrayList<>();
        Map<String, String> dictMap = new HashMap<>();
        dictMap.put("dictKey", "front");
        dictMap.put("dictValue", "前端");
        list.add(dictMap);
        dictMap = new HashMap<>();
        dictMap.put("dictKey", "back");
        dictMap.put("dictValue", "后端");
        list.add(dictMap);
        return list;
    }

    // 导出用到
    @Override
    public String toName(String dict, Object obj, String name, Object value) {
        if (TYPE.equals(dict)) {
            if ("front".equals(String.valueOf(value))) {
                return "前端";
            }
            return "后端";
        } else if (LANG.equals(dict)) {
            if ("zh".equals(String.valueOf(value))) {
                return "中文";
            }
            return "英文";
        }
        throw new RuntimeException("字典名称不正确");
    }

    // 导入用到
    @Override
    public String toValue(String dict, Object obj, String name, Object value) {
        if (TYPE.equals(dict)) {
            if ("前端".equals(String.valueOf(value))) {
                return "front";
            }
            return "back";
        } else if (LANG.equals(dict)) {
            if ("中文".equals(String.valueOf(value))) {
                return "zh";
            }
            return "en";
        }
        throw new RuntimeException("字典名称不正确");
    }
}
