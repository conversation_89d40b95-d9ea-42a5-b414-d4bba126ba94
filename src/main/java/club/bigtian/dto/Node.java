package club.bigtian.dto;

import java.util.List;

/**
 * @program: demo4
 * @description:
 * @author: bigtian
 * @since:
 * @create: 2023/5/18 21:30
 */

public class Node {
    private String processDefKey;
    private String processDefName;
    private List<NodeItem> eventList;
    private List<NodeItem> userTaskList;

    public String getProcessDefKey() {
        return processDefKey;
    }

    public void setProcessDefKey(String processDefKey) {
        this.processDefKey = processDefKey;
    }

    public String getProcessDefName() {
        return processDefName;
    }

    public void setProcessDefName(String processDefName) {
        this.processDefName = processDefName;
    }

    public List<NodeItem> getEventList() {
        return eventList;
    }

    public void setEventList(List<NodeItem> eventList) {
        this.eventList = eventList;
    }

    public List<NodeItem> getUserTaskList() {
        return userTaskList;
    }

    public void setUserTaskList(List<NodeItem> userTaskList) {
        this.userTaskList = userTaskList;
    }

    @Override
    public String toString() {
        return "Node{" +
                "processDefKey='" + processDefKey + '\'' +
                ", processDefName='" + processDefName + '\'' +
                ", eventList=" + eventList +
                ", userTaskList=" + userTaskList +
                '}';
    }
}
