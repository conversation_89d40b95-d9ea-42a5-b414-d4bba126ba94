package club.bigtian.dto;


/**
 * field info
 *
 * <AUTHOR> 2018-05-02 20:11:05
 */
public class FieldInfo {

    private String columnName;
    private String fieldName;
    private String fieldClass;
    private String swaggerClass;
    private String fieldComment;

    @Override
    public String toString() {
        return "FieldInfo{" +
                "columnName='" + columnName + '\'' +
                ", fieldName='" + fieldName + '\'' +
                ", fieldClass='" + fieldClass + '\'' +
                ", swaggerClass='" + swaggerClass + '\'' +
                ", fieldComment='" + fieldComment + '\'' +
                '}';
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldClass() {
        return fieldClass;
    }

    public void setFieldClass(String fieldClass) {
        this.fieldClass = fieldClass;
    }

    public String getSwaggerClass() {
        return swaggerClass;
    }

    public void setSwaggerClass(String swaggerClass) {
        this.swaggerClass = swaggerClass;
    }

    public String getFieldComment() {
        return fieldComment;
    }

    public void setFieldComment(String fieldComment) {
        this.fieldComment = fieldComment;
    }
}
