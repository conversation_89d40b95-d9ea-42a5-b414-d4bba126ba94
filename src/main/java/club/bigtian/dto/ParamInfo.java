package club.bigtian.dto;


import java.util.Map;

/**
 * Post data - ParamInfo
 *
 * <AUTHOR>
 */
public class ParamInfo {

    private String tableSql;
    private Map<String,Object> options;

    public String getTableSql() {
        return tableSql;
    }

    public void setTableSql(String tableSql) {
        this.tableSql = tableSql;
    }

    public Map<String, Object> getOptions() {
        return options;
    }

    public void setOptions(Map<String, Object> options) {
        this.options = options;
    }

    @Override
    public String toString() {
        return "ParamInfo{" +
                "tableSql='" + tableSql + '\'' +
                ", options=" + options +
                '}';
    }

    public static class NAME_CASE_TYPE {
        public static String CAMEL_CASE = "CamelCase";
        public static String UNDER_SCORE_CASE = "UnderScoreCase";
        public static String UPPER_UNDER_SCORE_CASE = "UpperUnderScoreCase";


        public static String getCamelCase() {
            return CAMEL_CASE;
        }

        public static void setCamelCase(String camelCase) {
            CAMEL_CASE = camelCase;
        }

        public static String getUnderScoreCase() {
            return UNDER_SCORE_CASE;
        }

        public static void setUnderScoreCase(String underScoreCase) {
            UNDER_SCORE_CASE = underScoreCase;
        }

        public static String getUpperUnderScoreCase() {
            return UPPER_UNDER_SCORE_CASE;
        }

        public static void setUpperUnderScoreCase(String upperUnderScoreCase) {
            UPPER_UNDER_SCORE_CASE = upperUnderScoreCase;
        }
    }

}
