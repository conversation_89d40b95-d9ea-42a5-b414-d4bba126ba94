package club.bigtian.dto;

/**
 * 流程环境配置
 */
public class FlowEnvironment {
    
    /**
     * 环境名称
     */
    private String name;
    
    /**
     * 环境标识符
     */
    private String env;
    
    /**
     * 环境URL
     */
    private String url;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 授权令牌
     */
    private String token;
    
    private String gateWayAccount;
    private String gateWayPwd;

    public FlowEnvironment() {
    }

    public FlowEnvironment(String name, String env, String url, String username, String password) {
        this.name = name;
        this.env = env;
        this.url = url;
        this.username = username;
        this.password = password;
    }

    public FlowEnvironment(String name, String env, String url, String username, String password, String gateWayAccount, String gateWayPwd) {
        this.name = name;
        this.env = env;
        this.url = url;
        this.username = username;
        this.password = password;
        this.gateWayAccount = gateWayAccount;
        this.gateWayPwd = gateWayPwd;
    }
    
    public FlowEnvironment(String name, String env, String url, String username, String password, String token) {
        this.name = name;
        this.env = env;
        this.url = url;
        this.username = username;
        this.password = password;
        this.token = token;
    }

    public String getGateWayAccount() {
        return gateWayAccount;
    }

    public void setGateWayAccount(String gateWayAccount) {
        this.gateWayAccount = gateWayAccount;
    }

    public String getGateWayPwd() {
        return gateWayPwd;
    }

    public void setGateWayPwd(String gateWayPwd) {
        this.gateWayPwd = gateWayPwd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return name + " [" + env + "]";
    }
} 