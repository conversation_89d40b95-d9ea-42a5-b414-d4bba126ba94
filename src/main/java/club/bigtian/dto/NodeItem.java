package club.bigtian.dto;

import java.util.List;

/**
 * @program: demo4
 * @description:
 * @author: bigtian
 * @since:
 * @create: 2023/5/18 21:29
 */

public class NodeItem {
    /**
     * 当前环节id
     */
    private String activityDefId;

    /**
     * 当前环节名称
     */
    private String activityDefName;
    /**
     * 角色、部门角色、人员
     */
    private String type;

    /**
     * 动态环节脚本
     */
    private String dyScript;

    /**
     * 驼峰
     */
    private String activityDefIdCame;
    /**
     * 常量
     */
    private String activityDefIdFinal;

    /**
     * 下一环节id
     */
    private String nextActivityDefId;
    private String nextActivityDefIdFinal;

    /**
     * 下一环节
     */
    private List<NodeItem> nextNodeList;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 接收人员
     */
    private String receive;

    private String deptCodeUpper;

    /**
     * 角色编码
     */
    private String roleCodeUpper;

    /**
     * 接收人员
     */
    private String receiveUpper;

    public String getDeptCodeUpper() {
        return deptCodeUpper;
    }

    public void setDeptCodeUpper(String deptCodeUpper) {
        this.deptCodeUpper = deptCodeUpper;
    }

    public String getRoleCodeUpper() {
        return roleCodeUpper;
    }

    public void setRoleCodeUpper(String roleCodeUpper) {
        this.roleCodeUpper = roleCodeUpper;
    }

    public String getReceiveUpper() {
        return receiveUpper;
    }

    public void setReceiveUpper(String receiveUpper) {
        this.receiveUpper = receiveUpper;
    }

    public List<NodeItem> getNextNodeList() {
        return nextNodeList;
    }

    public void setNextNodeList(List<NodeItem> nextNodeList) {
        this.nextNodeList = nextNodeList;
    }

    public String getActivityDefId() {
        return activityDefId;
    }

    public void setActivityDefId(String activityDefId) {
        this.activityDefId = activityDefId;
    }

    public String getActivityDefName() {
        return activityDefName;
    }

    public void setActivityDefName(String activityDefName) {
        this.activityDefName = activityDefName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDyScript() {
        return dyScript;
    }

    public void setDyScript(String dyScript) {
        this.dyScript = dyScript;
    }

    public String getActivityDefIdCame() {
        return activityDefIdCame;
    }

    public void setActivityDefIdCame(String activityDefIdCame) {
        this.activityDefIdCame = activityDefIdCame;
    }

    public String getActivityDefIdFinal() {
        return activityDefIdFinal;
    }

    public void setActivityDefIdFinal(String activityDefIdFinal) {
        this.activityDefIdFinal = activityDefIdFinal;
    }

    public String getNextActivityDefId() {
        return nextActivityDefId;
    }

    public void setNextActivityDefId(String nextActivityDefId) {
        this.nextActivityDefId = nextActivityDefId;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getReceive() {
        return receive;
    }

    public void setReceive(String receive) {
        this.receive = receive;
    }

    public String getNextActivityDefIdFinal() {
        return nextActivityDefIdFinal;
    }

    public void setNextActivityDefIdFinal(String nextActivityDefIdFinal) {
        this.nextActivityDefIdFinal = nextActivityDefIdFinal;
    }

    @Override
    public String toString() {
        return "NodeItem{" +
                "activityDefId='" + activityDefId + '\'' +
                ", activityDefName='" + activityDefName + '\'' +
                ", type='" + type + '\'' +
                ", dyScript='" + dyScript + '\'' +
                ", activityDefIdCame='" + activityDefIdCame + '\'' +
                ", activityDefIdFinal='" + activityDefIdFinal + '\'' +
                ", nextActivityDefId='" + nextActivityDefId + '\'' +
                ", nextActivityDefIdFinal='" + nextActivityDefIdFinal + '\'' +
                ", nextNodeList=" + nextNodeList +
                ", deptCode='" + deptCode + '\'' +
                ", roleCode='" + roleCode + '\'' +
                ", roleName='" + roleName + '\'' +
                ", receive='" + receive + '\'' +
                '}';
    }
}
