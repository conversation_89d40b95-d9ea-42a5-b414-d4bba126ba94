package club.bigtian.dto;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ApplicationDto {
    @JsonProperty("appDescribe")
    private String appDescribe;
    @JsonProperty("appId")
    private String appId;
    @JsonProperty("appName")
    private String appName;
    @JsonProperty("appStatus")
    private String appStatus;
    @JsonProperty("appType")
    private String appType;
    @JsonProperty("busiType")
    private String busiType;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("createUserCode")
    private String createUserCode;
    @JsonProperty("createUserName")
    private String createUserName;
    @JsonProperty("frontPort")
    private Object frontPort;
    @JsonProperty("indexUrl")
    private String indexUrl;
    @JsonProperty("initStatus")
    private String initStatus;
    @JsonProperty("isDeploy")
    private String isDeploy;
    @JsonProperty("isService")
    private String isService;
    @JsonProperty("rearPort")
    private Object rearPort;
    @JsonProperty("remark")
    private Object remark;
    @JsonProperty("sapCompanyCode")
    private String sapCompanyCode;
    @JsonProperty("sapOrgCode")
    private String sapOrgCode;
    @JsonProperty("sort")
    private Integer sort;
    @JsonProperty("updateTime")
    private String updateTime;
    @JsonProperty("updateUserCode")
    private Object updateUserCode;
    @JsonProperty("updateUserName")
    private Object updateUserName;
    @JsonProperty("wareHouseCode")
    private String wareHouseCode;

    public String getAppDescribe() {
        return appDescribe;
    }

    public void setAppDescribe(String appDescribe) {
        this.appDescribe = appDescribe;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(String appStatus) {
        this.appStatus = appStatus;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Object getFrontPort() {
        return frontPort;
    }

    public void setFrontPort(Object frontPort) {
        this.frontPort = frontPort;
    }

    public String getIndexUrl() {
        return indexUrl;
    }

    public void setIndexUrl(String indexUrl) {
        this.indexUrl = indexUrl;
    }

    public String getInitStatus() {
        return initStatus;
    }

    public void setInitStatus(String initStatus) {
        this.initStatus = initStatus;
    }

    public String getIsDeploy() {
        return isDeploy;
    }

    public void setIsDeploy(String isDeploy) {
        this.isDeploy = isDeploy;
    }

    public String getIsService() {
        return isService;
    }

    public void setIsService(String isService) {
        this.isService = isService;
    }

    public Object getRearPort() {
        return rearPort;
    }

    public void setRearPort(Object rearPort) {
        this.rearPort = rearPort;
    }

    public Object getRemark() {
        return remark;
    }

    public void setRemark(Object remark) {
        this.remark = remark;
    }

    public String getSapCompanyCode() {
        return sapCompanyCode;
    }

    public void setSapCompanyCode(String sapCompanyCode) {
        this.sapCompanyCode = sapCompanyCode;
    }

    public String getSapOrgCode() {
        return sapOrgCode;
    }

    public void setSapOrgCode(String sapOrgCode) {
        this.sapOrgCode = sapOrgCode;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Object getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(Object updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public Object getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(Object updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getWareHouseCode() {
        return wareHouseCode;
    }

    public void setWareHouseCode(String wareHouseCode) {
        this.wareHouseCode = wareHouseCode;
    }
}
