package club.bigtian.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class RoleDTO {
    @JsonProperty("appId")
    private String appId;
    @JsonProperty("appName")
    private String appName;
    @JsonProperty("authRole")
    private Object authRole;
    @JsonProperty("createDate")
    private Object createDate;
    @JsonProperty("creatorId")
    private Object creatorId;
    @JsonProperty("limitNumber")
    private Object limitNumber;
    @JsonProperty("orderByColumns")
    private Object orderByColumns;
    @JsonProperty("orgId")
    private Object orgId;
    @JsonProperty("pageNum")
    private Integer pageNum;
    @JsonProperty("pageSize")
    private Integer pageSize;
    @JsonProperty("postCodes")
    private Object postCodes;
    @JsonProperty("remark")
    private Object remark;
    @JsonProperty("roleId")
    private String roleId;
    @JsonProperty("roleModule")
    private Object roleModule;
    @JsonProperty("roleName")
    private String roleName;
    @JsonProperty("roleType")
    private String roleType;
    @JsonProperty("sort")
    private Integer sort;
    @JsonProperty("sortColumn")
    private Object sortColumn;
    @JsonProperty("sortColumns")
    private Object sortColumns;
    @JsonProperty("status")
    private String status;
    @JsonProperty("updateDate")
    private Object updateDate;
    @JsonProperty("updatorId")
    private Object updatorId;
    @JsonProperty("userId")
    private Object userId;
}
