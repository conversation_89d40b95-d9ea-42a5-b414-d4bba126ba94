package club.bigtian.dto;

import java.util.List;
import java.util.Set;

public class TableDTO {


    private  String tableName;
    private  String tableComment;

    List<ColumnDTO> columnList;

    Set<String> packageSet;

    public Set<String> getPackageSet() {
        return packageSet;
    }

    public String getTableComment() {
        return tableComment;
    }

    public void setTableComment(String tableComment) {
        this.tableComment = tableComment;
    }

    public void setPackageSet(Set<String> packageSet) {
        this.packageSet = packageSet;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<ColumnDTO> getColumnList() {
        return columnList;
    }

    public void setColumnList(List<ColumnDTO> columnList) {
        this.columnList = columnList;
    }

    @Override
    public String toString() {
        return "TableDTO{" +
                "tableName='" + tableName + '\'' +
                ", columnList=" + columnList +
                '}';
    }
}
