package club.bigtian.acttion;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.roots.ProjectRootManager;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.*;
import com.intellij.usageView.UsageInfo;
import com.intellij.usages.*;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class FindMagicValuesAction extends AnAction {
    @Override
    public void actionPerformed(AnActionEvent e) {
        // 获取当前项目
        Project project = e.getProject();
        if (project == null) {
            return;
        }

        // 创建一个后台任务来查找魔法值
        Task.Backgroundable task = new Task.Backgroundable(project, "Finding Magic Values") {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                List<Usage> list = new ArrayList<>();

                UsageViewPresentation presentation = new UsageViewPresentation();
                presentation.setTabText("Magic Values");
                presentation.setTargetsNodeText("Magic Values");
                // 使用 Application.runReadAction 来读取 PSI
                ApplicationManager.getApplication().runReadAction(() -> {
                    // 获取项目中的所有 Java 文件
                    FileType javaFileType = FileTypeManager.getInstance().getFileTypeByExtension("java");
                    ProjectRootManager.getInstance(project).getFileIndex().iterateContent(file -> {
                        if (file.getFileType() == javaFileType) {
                            PsiFile psiFile = PsiManager.getInstance(project).findFile(file);
                            if (psiFile instanceof PsiJavaFile) {
                                PsiJavaFile javaFile = (PsiJavaFile) psiFile;

                                // 遍历所有的类
                                for (PsiClass psiClass : javaFile.getClasses()) {
                                    // 遍历所有的方法
                                    for (PsiMethod method : psiClass.getMethods()) {
                                        if (method.getName().equals("toString")) {
                                            continue;
                                        }
                                        // 遍历所有的语句
                                        method.accept(new JavaRecursiveElementVisitor() {
                                            @Override
                                            public void visitLiteralExpression(PsiLiteralExpression expression) {
                                                // 在这里检查魔法值
                                                if (expression.getValue() instanceof String) {
                                                    if ((expression.getValue() instanceof String) && !isInAnnotation(expression)) {
                                                        // 创建一个 OpenFileDescriptor 对象
                                                        String val = expression.getValue().toString();
                                                        boolean containsChinese = ReUtil.contains("[\\u4e00-\\u9fa5]", val);
                                                        if (containsChinese) {
                                                            PsiFile psiFile = expression.getContainingFile();
                                                            Document document = FileDocumentManager.getInstance().getDocument(psiFile.getVirtualFile());
                                                            String lineText = "";
                                                            if (document != null) {
                                                                int lineNumber = document.getLineNumber(expression.getTextRange().getStartOffset());
                                                                int lineStartOffset = document.getLineStartOffset(lineNumber);
                                                                int lineEndOffset = document.getLineEndOffset(lineNumber);
                                                                lineText = document.getText(new TextRange(lineStartOffset, lineEndOffset)).trim();
                                                            }
                                                            if (!StrUtil.startWithAnyIgnoreCase(lineText, "log")) {
                                                                list.add(findMagicValues(expression));
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            private boolean isInAnnotation(PsiElement element) {
                                                while (element != null) {
                                                    if (element instanceof PsiAnnotation) {
                                                        return true;
                                                    }
                                                    element = element.getParent();
                                                }
                                                return false;
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        return true;
                    });
                    ApplicationManager.getApplication().invokeLater(() -> {


                        UsageViewManager.getInstance(project).showUsages(UsageTarget.EMPTY_ARRAY, list.toArray(new Usage[0]), presentation);

                    });
                });
            }
        };

        // 启动后台任务
        ProgressManager.getInstance().run(task);
    }


    private Usage findMagicValues(PsiLiteralExpression magicValue) {
        // 创建一个 Usage 对象并将它添加到 UsageView
        UsageInfo usageInfo = new UsageInfo(magicValue);
        return new UsageInfo2UsageAdapter(usageInfo);
    }
}