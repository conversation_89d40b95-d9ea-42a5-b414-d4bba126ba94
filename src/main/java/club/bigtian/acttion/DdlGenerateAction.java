package club.bigtian.acttion;

import club.bigtian.dialogs.DDLDialog;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.DataContext;
import org.jetbrains.annotations.NotNull;


public class DdlGenerateAction extends AnAction {
    /**
     * Performs the action logic.
     * <p/>
     * It is called on the UI thread with all data in the provided {@link DataContext} instance.
     *
     * @param e
     * @see #beforeActionPerformedUpdate(AnActionEvent)
     */
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        new DDLDialog(e).show();
    }


}
