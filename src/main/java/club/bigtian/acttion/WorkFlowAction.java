package club.bigtian.acttion;

import club.bigtian.dialogs.WorkFlowDialog;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;

public class WorkFlowAction extends AnAction {

    Project project;
    private String name;

    @Override
    public void actionPerformed(AnActionEvent e) {
        this.project = e.getProject();
        WorkFlowDialog dialog = new WorkFlowDialog(e);
        dialog.show();
    }

}
