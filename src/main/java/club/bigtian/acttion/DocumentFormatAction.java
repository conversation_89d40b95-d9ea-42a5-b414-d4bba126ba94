package club.bigtian.acttion;

import club.bigtian.util.DocumentUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.*;
import com.intellij.psi.codeStyle.CodeStyleManager;
import com.intellij.psi.impl.source.javadoc.PsiDocTokenImpl;
import com.intellij.psi.javadoc.PsiDocComment;
import com.intellij.util.IncorrectOperationException;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用于单行注释转多行注释
 */
public class DocumentFormatAction extends AnAction {

    @Override
    public void actionPerformed(AnActionEvent e) {
        Project project = e.getProject();
        PsiFile psiFiles = e.getData(CommonDataKeys.PSI_FILE);
        AtomicInteger preLineNumber = new AtomicInteger(-1);
        if (psiFiles instanceof PsiJavaFile) {
            PsiJavaFile psiJavaFile = (PsiJavaFile) psiFiles;
            PsiClass psiClass = psiJavaFile.getClasses()[0];
            HashMap<PsiElement, PsiDocComment> docCommentHashMap = new HashMap<>();
            //设置属性注释
            setFieldComment(psiClass, preLineNumber, docCommentHashMap);
//            //设置方法注释
//            setMethodComment(psiClass, preLineNumber, docCommentHashMap);

            WriteCommandAction.runWriteCommandAction(project, () -> {
                for (Map.Entry<PsiElement, PsiDocComment> entry : docCommentHashMap.entrySet()) {
                    PsiElement key = entry.getKey();
                    PsiDocComment value = entry.getValue();
                    key.addBefore(value, key.getFirstChild());
                }
                PsiElement[] children = psiClass.getChildren();
                PsiElementFactory elementFactory = JavaPsiFacade.getElementFactory(project);

                for (PsiElement child : children) {

                    if (child.getText().contains("//")) {
                        String text = child.getText();
                        if (text.contains("//")) {
                            if (child instanceof PsiField) {
                                try {
                                    child.replace(elementFactory.createFieldFromText(text.replaceAll("//\\s*(.*)", "").trim(), null));
                                } catch (IncorrectOperationException ex) {
                                    Log.get().warn("格式化注释失败，原因：{}", ex.getMessage());
                                }
                                continue;
                            }
                            if (child instanceof PsiMethod) {
                                String result = text.replaceAll("(?m)^(\\s*//[^\\n]*)(?![^{]*\\})", "").trim().replaceAll("/ //\\s*(.*?)\\s* ", "*/\n");

                                try {
                                    child.replace(elementFactory.createMethodFromText(result, null));
                                } catch (IncorrectOperationException ex) {
                                    Log.get().warn("格式化注释失败，原因：{}", ex.getMessage());
                                }
                                continue;
                            }
                            if (child instanceof PsiClass) {
                                continue;
                            }
                            child.delete();
                        }
                    }
                }
                //格式化代码
                CodeStyleManager codeStyleManager = CodeStyleManager.getInstance(project);
                codeStyleManager.reformat(psiJavaFile);
            });
        }
    }

    /**
     * 设置属性注释
     *
     * @param psiClass
     * @param preLineNumber
     * @param docCommentHashMap
     */
    private static void setFieldComment(PsiClass psiClass, AtomicInteger preLineNumber, HashMap<PsiElement, PsiDocComment> docCommentHashMap) {

        for (PsiElement element : psiClass.getChildren()) {
            if (!(element instanceof PsiField) && !(element instanceof PsiMethod)) {
                continue;
            }
            PsiDocComment docComment = null;
            if (element instanceof PsiField) {
                docComment = ((PsiField) element).getDocComment();
            } else if (element instanceof PsiMethod) {
                docComment = ((PsiMethod) element).getDocComment();
            }
            String commentResult = "";
            if (ObjectUtil.isNotNull(docComment)) {
                StringBuilder sb = new StringBuilder();
                for (PsiElement element1 : docComment.getDescriptionElements()) {
                    if (element1 instanceof PsiDocTokenImpl) {
                        sb.append(element1.getText());
                    }
                }
                commentResult = sb.toString().trim().replaceAll("\\*", "");
                WriteCommandAction.runWriteCommandAction(element.getProject(), () -> {
                    ((PsiDocCommentOwner) element).getDocComment().delete();
                });
            }
            String comment = getComment(element, preLineNumber, commentResult);
            if (StrUtil.isBlank(comment)) {
                continue;
            }
            PsiDocComment document = DocumentUtils.createDocument(element, comment);
            docCommentHashMap.put(element, document);

        }
    }



    /**
     * 获取字段的注释
     *
     * @param element
     * @param preLineNumber 上一个字段的行号
     * @param commentResult
     * @return
     */
    private static String getComment(PsiElement element, AtomicInteger preLineNumber, String commentResult) {

        Project project = element.getProject();
        PsiFile psiFile = element.getContainingFile();
        com.intellij.openapi.editor.Document document = PsiDocumentManager.getInstance(project).getDocument(psiFile);
        int lineNumber = document.getLineNumber(element.getTextOffset());

        int temLine = lineNumber; //
        if (element instanceof PsiMethod) {
            temLine = document.getLineNumber(element.getTextRange().getEndOffset());
        }
        String lineText = document.getText(new TextRange(document.getLineStartOffset(lineNumber), document.getLineEndOffset(lineNumber)));
        Matcher matcher = Pattern.compile("//\\s*(.*)").matcher(lineText); // 使用正则表达式匹配//后面的注释
        String commentText = "";
        if (matcher.find()) {
            //解决 注释在字段后面同一行的情况
            commentText = matcher.group(1);
        } else {
            //解决 注释在字段前面的情况，但是不清楚是前面几行
            while (lineNumber > preLineNumber.get()) {
                lineText = document.getText(new TextRange(document.getLineStartOffset(lineNumber), document.getLineEndOffset(lineNumber)));
                if (lineText.contains(" class ")) {
                    break;
                }
                matcher = Pattern.compile("//\\s*(.*)").matcher(lineText);
                while (matcher.find()) {
                    commentText = matcher.group(1) + "\n* " + commentText + "\n* ";
                }
                lineNumber = lineNumber - 1;
            }
        }
        preLineNumber.set(temLine);
        return commentResult + "\n" + commentText;
    }


}
