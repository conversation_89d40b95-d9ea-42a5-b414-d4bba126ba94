package club.bigtian.acttion;

import club.bigtian.dialogs.ExcelToSqlDialog;
import com.intellij.database.psi.DbTable;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.LangDataKeys;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiElement;
import org.jetbrains.annotations.NotNull;

public class ExcelToSqlAction extends AnAction {
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        // Get the current project
        Project project = e.getProject();
        if (project == null) {
            return;
        }

        // Get the selected database table (if any)
        PsiElement[] psiElements = e.getData(LangDataKeys.PSI_ELEMENT_ARRAY);
        DbTable selectedTable = null;
        if (psiElements != null && psiElements.length > 0) {
            for (PsiElement element : psiElements) {
                if (element instanceof DbTable) {
                    selectedTable = (DbTable) element;
                    break;
                }
            }
        }

        // Open the Excel to SQL dialog
        ExcelToSqlDialog dialog = new ExcelToSqlDialog(project, selectedTable);
        dialog.show();
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        // Enable the action only in database context
        PsiElement[] psiElements = e.getData(LangDataKeys.PSI_ELEMENT_ARRAY);
        boolean enableAction = false;
        
        if (psiElements != null && psiElements.length > 0) {
            for (PsiElement element : psiElements) {
                if (element instanceof DbTable) {
                    enableAction = true;
                    break;
                }
            }
        }
        
        e.getPresentation().setEnabledAndVisible(enableAction);
    }
} 