package club.bigtian.dialogs;

import club.bigtian.dto.Node;
import club.bigtian.dto.NodeItem;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.FixedSizeButton;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 节点选择对话框，用于选择要导出的流程节点
 */
public class NodeSelectionDialog extends DialogWrapper {
    private final Map<Node, List<JCheckBox>> nodeCheckboxes = new HashMap<>();
    private final JPanel mainPanel = new JPanel(new BorderLayout());
    private final JTabbedPane tabbedPane = new JTabbedPane();
    private int currentIndex = 0;

    public NodeSelectionDialog(List<Node> nodes) {
        super(true);
        setTitle("选择要导出的节点");
        
        for (Node node : nodes) {
            // 创建主面板使用BorderLayout
            JPanel nodePanel = new JPanel(new BorderLayout());
            
            // 头部面板 - 将保持固定，不随滚动
            JPanel headerPanel = new JPanel(new BorderLayout());
            JPanel selectAllPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 0));
            JCheckBox selectAllCheckBox = new JCheckBox("全选");
            selectAllCheckBox.setMargin(new Insets(0, 0, 0, 0));
            selectAllPanel.add(selectAllCheckBox);
            headerPanel.add(selectAllPanel, BorderLayout.NORTH);
            
            // 分隔线 - 也保持固定
            JSeparator separator = new JSeparator(JSeparator.HORIZONTAL);
            separator.setForeground(Color.LIGHT_GRAY);
            JPanel separatorPanel = new JPanel(new BorderLayout());
            separatorPanel.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 0));
            separatorPanel.add(separator, BorderLayout.CENTER);
            headerPanel.add(separatorPanel, BorderLayout.CENTER);
            
            // 创建滚动内容面板
            JPanel checkboxPanel = new JPanel();
            checkboxPanel.setLayout(new BoxLayout(checkboxPanel, BoxLayout.Y_AXIS));
            checkboxPanel.setBorder(BorderFactory.createEmptyBorder(0, 5, 0, 5));
            
            List<JCheckBox> checkBoxes = new ArrayList<>();
            for (NodeItem item : node.getUserTaskList()) {
                if (!item.getActivityDefId().startsWith("sid") && !"end".equalsIgnoreCase(item.getActivityDefId())) {
                    JCheckBox checkBox = new JCheckBox(item.getActivityDefName());
                    checkBox.setMargin(new Insets(0, 0, 0, 0));
                    checkBox.setBorder(BorderFactory.createEmptyBorder(0, 2, 0, 2));
                    checkBox.setSelected(true);
                    checkBox.putClientProperty("nodeItem", item);
                    checkBoxes.add(checkBox);
                    
                    JPanel itemPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 2, 0));
                    itemPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 20));
                    itemPanel.add(checkBox);
                    checkboxPanel.add(itemPanel);
                }
            }
            
            nodeCheckboxes.put(node, checkBoxes);
            
            // 全选复选框事件处理
            selectAllCheckBox.setSelected(true);
            selectAllCheckBox.addActionListener(e -> {
                boolean selected = selectAllCheckBox.isSelected();
                checkBoxes.forEach(cb -> cb.setSelected(selected));
            });
            
            // 监听各复选框状态，更新全选状态
            for (JCheckBox checkBox : checkBoxes) {
                checkBox.addActionListener(e -> {
                    boolean allSelected = checkBoxes.stream().allMatch(JCheckBox::isSelected);
                    selectAllCheckBox.setSelected(allSelected);
                });
            }
            
            // 只有checkboxPanel放入滚动区域
            JScrollPane scrollPane = new JScrollPane(checkboxPanel);
            scrollPane.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
            scrollPane.getVerticalScrollBar().setUnitIncrement(16);
            
            // 使滚动区域高度根据内容自适应，但有最小和最大限制
            int itemHeight = 20; // 每个复选框项的高度
            int preferredHeight;
            if (checkBoxes.isEmpty()) {
                preferredHeight = 50; // 最小高度
            } else if (checkBoxes.size() <= 5) {
                preferredHeight = checkBoxes.size() * itemHeight + 10; // 少量选项时紧凑显示
            } else {
                preferredHeight = Math.min(300, checkBoxes.size() * itemHeight + 20); // 最大高度300px
            }
            scrollPane.setPreferredSize(new Dimension(400, preferredHeight));
            
            // 组装完整面板
            nodePanel.add(headerPanel, BorderLayout.NORTH);
            nodePanel.add(scrollPane, BorderLayout.CENTER);
            
            tabbedPane.addTab(node.getProcessDefName(), nodePanel);
        }
        
        mainPanel.add(tabbedPane, BorderLayout.CENTER);
        
        if (nodes.size() > 1) {
            JPanel navigationPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
            
            // 使用FixedSizeButton和图标
            FixedSizeButton prevBtn = new FixedSizeButton();
            prevBtn.setIcon(AllIcons.General.ArrowLeft);
            
            FixedSizeButton nextBtn = new FixedSizeButton();
            nextBtn.setIcon(AllIcons.General.ArrowRight);
            
            JLabel countLabel = new JLabel(String.format("1 / %d", nodes.size()));
            
            prevBtn.addActionListener(e -> {
                if (currentIndex > 0) {
                    currentIndex--;
                    tabbedPane.setSelectedIndex(currentIndex);
                    countLabel.setText(String.format("%d / %d", currentIndex + 1, nodes.size()));
                }
            });
            
            nextBtn.addActionListener(e -> {
                if (currentIndex < nodes.size() - 1) {
                    currentIndex++;
                    tabbedPane.setSelectedIndex(currentIndex);
                    countLabel.setText(String.format("%d / %d", currentIndex + 1, nodes.size()));
                }
            });
            
            tabbedPane.addChangeListener(e -> {
                currentIndex = tabbedPane.getSelectedIndex();
                countLabel.setText(String.format("%d / %d", currentIndex + 1, nodes.size()));
            });
            
            navigationPanel.add(prevBtn);
            navigationPanel.add(countLabel);
            navigationPanel.add(nextBtn);
            mainPanel.add(navigationPanel, BorderLayout.SOUTH);
        }
        
        init();
    }

    @Nullable
    @Override
    protected JComponent createCenterPanel() {
        return mainPanel;
    }
    
    /**
     * 获取选中的节点
     * @return 选中的节点和节点项的映射
     */
    public Map<Node, List<NodeItem>> getSelectedNodes() {
        Map<Node, List<NodeItem>> selectedNodes = new HashMap<>();
        
        for (Map.Entry<Node, List<JCheckBox>> entry : nodeCheckboxes.entrySet()) {
            List<NodeItem> selectedItems = entry.getValue().stream()
                    .filter(JCheckBox::isSelected)
                    .map(cb -> (NodeItem) cb.getClientProperty("nodeItem"))
                    .collect(Collectors.toList());
            
            if (!selectedItems.isEmpty()) {
                selectedNodes.put(entry.getKey(), selectedItems);
            }
        }
        
        return selectedNodes;
    }
} 