package club.bigtian.dialogs;

import club.bigtian.util.ExcelUtil;
import club.bigtian.util.SqlGeneratorUtil;
import com.intellij.database.model.DasColumn;
import com.intellij.database.psi.DbTable;
import com.intellij.database.util.DasUtil;
import com.intellij.openapi.fileChooser.FileChooser;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.ComboBox;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.ui.JBSplitter;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.table.JBTable;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import javax.swing.Timer;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionEvent;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.util.*;
import java.util.List;

public class ExcelToSqlDialog extends DialogWrapper {
    private final Project project;
    private final DbTable selectedTable;
    private final JPanel mainPanel;
    private final JTextField excelPathField;
    private final JPanel mappingPanel;
    private final JComboBox<String> sqlTypeComboBox;
    private final JTextArea sqlPreviewArea;
    private final JButton executeButton;
    private final JTable previewTable;
    private final JTable mappingTable;
    private DefaultTableModel tableModel;
    private DefaultTableModel mappingTableModel;
    private List<String> excelHeaders;
    private List<List<String>> excelData;
    private Map<String, String> columnMappings; // Excel header to DB column mapping
    private Map<String, Boolean> primaryKeyMap; // Column name to isPrimaryKey flag
    private Map<String, String> defaultValueMap; // Column name to default value
    private JCheckBox templateOnlyCheckBox; // Checkbox for template-only mode
    private List<ColumnInfo> dbColumnInfos; // Store column info with comments

    // Class to hold column info including name and comment
    private static class ColumnInfo {
        private final String name;
        private final String comment;
        
        public ColumnInfo(String name, String comment) {
            this.name = name != null ? name : "";
            this.comment = (comment != null && !comment.isEmpty()) ? comment : (name != null ? name : "");
        }
        
        public String getName() {
            return name;
        }
        
        public String getComment() {
            return comment;
        }
        
        // Used for search matching
        public boolean matches(String searchText) {
            if (searchText == null || searchText.isEmpty()) {
                return true;
            }
            
            String lowerCaseSearch = searchText.toLowerCase();
            return name.toLowerCase().contains(lowerCaseSearch) || 
                   comment.toLowerCase().contains(lowerCaseSearch);
        }
        
        @Override
        public String toString() {
            return comment + " [" + name + "]";
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            ColumnInfo that = (ColumnInfo) obj;
            return Objects.equals(name, that.name);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(name);
        }
    }
    
    // 使用增强的ComboBox替代TextFieldWithAutoCompletion
    private class ColumnComboBoxEditor extends DefaultCellEditor {
        private final List<ColumnInfo> allItems;
        
        public ColumnComboBoxEditor(List<ColumnInfo> items) {
            super(new JComboBox<>());
            this.allItems = new ArrayList<>(items);
            
            JComboBox<ColumnInfo> comboBox = (JComboBox<ColumnInfo>) getComponent();
            
            // 配置ComboBox
            comboBox.setEditable(true);
            comboBox.setMaximumRowCount(15);
            
            // 添加所有列信息
            DefaultComboBoxModel<ColumnInfo> model = new DefaultComboBoxModel<>();
            for (ColumnInfo item : items) {
                model.addElement(item);
            }
            comboBox.setModel(model);
            
            // 添加列表渲染器以显示列信息包含注释
            comboBox.setRenderer(new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
                    if (value instanceof ColumnInfo) {
                        value = ((ColumnInfo) value).toString();
                    }
                    return super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                }
            });
            
            // 添加搜索功能到编辑器组件
            final JTextField editorComponent = (JTextField) comboBox.getEditor().getEditorComponent();
            
            // 防止按键触发ComboBox的默认搜索行为
            comboBox.setKeySelectionManager((aKey, aModel) -> {
                return -1; // 禁用默认搜索行为
            });
            
            // 添加按键监听器实现搜索
            editorComponent.addKeyListener(new KeyAdapter() {
                private String lastText = "";
                private Timer searchTimer;
                
                @Override
                public void keyReleased(KeyEvent e) {
                    // 忽略特殊键
                    if (e.getKeyCode() == KeyEvent.VK_UP || e.getKeyCode() == KeyEvent.VK_DOWN ||
                        e.getKeyCode() == KeyEvent.VK_ENTER || e.getKeyCode() == KeyEvent.VK_ESCAPE) {
                        return;
                    }
                    
                    String text = editorComponent.getText();
                    
                    // 如果文本没有变化，不处理
                    if (text.equals(lastText)) {
                        return;
                    }
                    lastText = text;
                    
                    // 延迟搜索以提高性能
                    if (searchTimer != null) {
                        searchTimer.stop();
                    }
                    
                    searchTimer = new Timer(200, evt -> {
                        performSearch(text, comboBox);
                    });
                    searchTimer.setRepeats(false);
                    searchTimer.start();
                }
                
                // 执行搜索并显示结果
                private void performSearch(String text, JComboBox<ColumnInfo> comboBox) {
                    SwingUtilities.invokeLater(() -> {
                        // 使用当前文本过滤列表项
                        DefaultComboBoxModel<ColumnInfo> filteredModel = new DefaultComboBoxModel<>();
                        
                        if (text.isEmpty()) {
                            // 显示所有项
                            for (ColumnInfo item : allItems) {
                                filteredModel.addElement(item);
                            }
                        } else {
                            // 过滤匹配项
                            for (ColumnInfo item : allItems) {
                                if (item.matches(text)) {
                                    filteredModel.addElement(item);
                                }
                            }
                        }
                        
                        // 保存当前文本
                        String currentText = editorComponent.getText();
                        
                        // 更新模型
                        comboBox.setModel(filteredModel);
                        
                        // 还原编辑器文本
                        editorComponent.setText(currentText);
                        
                        // 如果有结果，显示下拉列表
                        if (filteredModel.getSize() > 0) {
                            comboBox.showPopup();
                            // 设置文本光标位置
                            editorComponent.setCaretPosition(currentText.length());
                        }
                    });
                }
            });
            
            // 添加项选择监听器
            comboBox.addActionListener(e -> {
                Object selected = comboBox.getSelectedItem();
                if (selected instanceof ColumnInfo) {
                    // 防止单元格编辑器的频繁更新
                    SwingUtilities.invokeLater(() -> stopCellEditing());
                }
            });
        }
        
        @Override
        public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
            JComboBox<ColumnInfo> comboBox = (JComboBox<ColumnInfo>) super.getTableCellEditorComponent(table, value, isSelected, row, column);
            
            if (value instanceof ColumnInfo) {
                comboBox.setSelectedItem(value);
            } else if (value != null) {
                String strValue = value.toString();
                
                // 尝试匹配ColumnInfo
                for (ColumnInfo info : allItems) {
                    if (info.getName().equals(strValue) || info.toString().equals(strValue)) {
                        comboBox.setSelectedItem(info);
                        return comboBox;
                    }
                }
                
                // 如果没有匹配项，设置为文本
                comboBox.getEditor().setItem(strValue);
            } else {
                comboBox.setSelectedItem(null);
            }
            
            return comboBox;
        }
        
        @Override
        public Object getCellEditorValue() {
            JComboBox<ColumnInfo> comboBox = (JComboBox<ColumnInfo>) getComponent();
            Object selected = comboBox.getSelectedItem();
            
            if (selected instanceof ColumnInfo) {
                return selected;
            }
            
            // 尝试匹配文本与列信息
            String text = selected != null ? selected.toString() : "";
            if (text.isEmpty()) {
                return "";
            }
            
            // 提取格式为"comment [name]"的列名
            if (text.contains("[") && text.endsWith("]")) {
                try {
                    String columnName = text.substring(text.lastIndexOf("[") + 1, text.length() - 1);
                    for (ColumnInfo info : allItems) {
                        if (info.getName().equals(columnName)) {
                            return info;
                        }
                    }
                } catch (IndexOutOfBoundsException ex) {
                    // 忽略并尝试直接匹配
                }
            }
            
            // 尝试直接匹配
            for (ColumnInfo info : allItems) {
                if (info.toString().equals(text) || info.getName().equals(text)) {
                    return info;
                }
            }
            
            // 如果没有匹配项，返回文本
            return text;
        }
    }

    public ExcelToSqlDialog(Project project, DbTable selectedTable) {
        super(project, true);
        this.project = project;
        this.selectedTable = selectedTable;
        this.columnMappings = new HashMap<>();
        this.primaryKeyMap = new HashMap<>();
        this.defaultValueMap = new HashMap<>();
        this.dbColumnInfos = new ArrayList<>();
        
        // Initialize components
        mainPanel = new JPanel(new BorderLayout());
        
        // Top panel with excel selection
        JPanel topPanel = new JPanel(new BorderLayout());
        excelPathField = new JTextField();
        excelPathField.setEditable(false);
        JButton browseButton = new JButton("Browse");
        browseButton.addActionListener(this::onBrowseExcel);
        topPanel.add(excelPathField, BorderLayout.CENTER);
        topPanel.add(browseButton, BorderLayout.EAST);
        
        // SQL type selection and preview panel
        JPanel sqlPanel = new JPanel(new BorderLayout());
        JPanel sqlTypePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        sqlTypePanel.add(new JLabel("SQL Type:"));
        sqlTypeComboBox = new ComboBox<>(new String[]{"INSERT", "UPDATE", "DELETE"});
        sqlTypePanel.add(sqlTypeComboBox);
        
        // Add template-only checkbox for INSERT mode
        templateOnlyCheckBox = new JCheckBox("仅生成模板语句", true);
        sqlTypePanel.add(templateOnlyCheckBox);
        
        sqlPanel.add(sqlTypePanel, BorderLayout.NORTH);
        
        sqlPreviewArea = new JTextArea();
        sqlPreviewArea.setEditable(false);
        JScrollPane sqlScrollPane = new JBScrollPane(sqlPreviewArea);
        sqlPanel.add(sqlScrollPane, BorderLayout.CENTER);
        
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton generateButton = new JButton("生成SQL");
        generateButton.addActionListener(this::onGenerateSql);
        JButton copyButton = new JButton("复制SQL");
        copyButton.addActionListener(this::onCopySql);
        executeButton = new JButton("执行SQL");
        executeButton.addActionListener(this::onExecuteSql);
        
        buttonPanel.add(generateButton);
        buttonPanel.add(copyButton);
        buttonPanel.add(executeButton);
        sqlPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        // Mapping panel (will be populated after selecting Excel file)
        mappingPanel = new JPanel(new BorderLayout());
        mappingPanel.setBorder(BorderFactory.createTitledBorder("字段映射"));
        
        // Add mapping table toolbar with add/remove buttons
        JPanel mappingToolbar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JButton addMappingButton = new JButton("新增映射");
        addMappingButton.addActionListener(this::onAddMapping);
        JButton removeMappingButton = new JButton("删除映射");
        removeMappingButton.addActionListener(this::onRemoveMapping);
        mappingToolbar.add(addMappingButton);
        mappingToolbar.add(removeMappingButton);
        mappingPanel.add(mappingToolbar, BorderLayout.NORTH);
        
        // Create mapping table with columns: Source Field, Target Field, Primary Key, Default Value
        String[] mappingColumns = {"源字段", "目标字段", "主键", "默认值"};
        mappingTableModel = new DefaultTableModel(mappingColumns, 0) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                return columnIndex == 2 ? Boolean.class : String.class;
            }
            
            @Override
            public boolean isCellEditable(int row, int column) {
                return column > 0; // Only target field, primary key and default value are editable
            }
        };
        
        mappingTable = new JBTable(mappingTableModel);
        mappingTable.getColumnModel().getColumn(0).setPreferredWidth(150);
        mappingTable.getColumnModel().getColumn(1).setPreferredWidth(250);
        mappingTable.getColumnModel().getColumn(2).setPreferredWidth(50);
        mappingTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        
        mappingPanel.add(new JBScrollPane(mappingTable), BorderLayout.CENTER);
        
        // Preview table
        tableModel = new DefaultTableModel();
        previewTable = new JBTable(tableModel);
        JScrollPane tableScrollPane = new JBScrollPane(previewTable);
        tableScrollPane.setBorder(BorderFactory.createTitledBorder("数据预览"));
        
        // Create a split pane for the bottom section
        JBSplitter bottomSplitter = new JBSplitter(true, 0.4f);
        bottomSplitter.setFirstComponent(mappingPanel);
        
        JBSplitter rightSplitter = new JBSplitter(false, 0.5f);
        rightSplitter.setFirstComponent(tableScrollPane);
        rightSplitter.setSecondComponent(sqlPanel);
        
        bottomSplitter.setSecondComponent(rightSplitter);
        
        // Add all components to main panel
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(bottomSplitter, BorderLayout.CENTER);
        
        // Initialize dialog
        setTitle("Excel to SQL");
        init();
        setSize(1000, 700);
        
        // Load database columns if table is selected
        if (selectedTable != null) {
            loadDatabaseColumns();
        }
        
        // Add listeners
        sqlTypeComboBox.addActionListener(e -> {
            String selectedType = (String) sqlTypeComboBox.getSelectedItem();
            templateOnlyCheckBox.setEnabled("INSERT".equals(selectedType));
        });
        
        // 暂时禁用执行按钮
        executeButton.setEnabled(false);
        executeButton.setToolTipText("执行SQL功能尚未实现");
    }

    private void loadDatabaseColumns() {
        dbColumnInfos.clear();
        primaryKeyMap.clear();
        
        // Get database columns with comments
        if (selectedTable == null) {
            return;
        }
        
        for (DasColumn column : DasUtil.getColumns(selectedTable)) {
            String columnName = column.getName();
            String comment = column.getComment();
            
            if (columnName == null || columnName.isEmpty()) {
                continue;
            }
            
            dbColumnInfos.add(new ColumnInfo(columnName, comment));
            
            // Mark primary keys
            if (DasUtil.isPrimary(column)) {
                primaryKeyMap.put(columnName, true);
            } else {
                primaryKeyMap.put(columnName, false);
            }
        }
        
        // 如果没有找到任何列
        if (dbColumnInfos.isEmpty()) {
            Messages.showWarningDialog("所选表没有列信息", "警告");
        }
    }

    private void onBrowseExcel(ActionEvent e) {
        FileChooserDescriptor descriptor = new FileChooserDescriptor(true, false, false, false, false, false)
                .withFileFilter(file -> {
                    String extension = file.getExtension();
                    return extension != null && (extension.equalsIgnoreCase("xls") || extension.equalsIgnoreCase("xlsx"));
                })
                .withTitle("Select Excel File");
        
        VirtualFile[] files = FileChooser.chooseFiles(descriptor, project, null);
        if (files.length > 0) {
            VirtualFile selectedFile = files[0];
            excelPathField.setText(selectedFile.getPath());
            
            try {
                // Read excel file
                excelData = ExcelUtil.readExcel(selectedFile.getPath());
                if (excelData == null || excelData.isEmpty()) {
                    Messages.showErrorDialog("Excel文件为空或格式错误", "错误");
                    return;
                }
                
                // Get headers from first row
                excelHeaders = excelData.get(0);
                excelData.remove(0); // Remove header row
                
                if (excelHeaders.isEmpty()) {
                    Messages.showErrorDialog("Excel文件没有表头", "错误");
                    return;
                }
                
                if (excelData.isEmpty()) {
                    Messages.showWarningDialog("Excel文件没有数据行，将只生成模板SQL", "警告");
                }
                
                // Update preview table
                updatePreviewTable();
                
                // Create mapping UI
                createMappingTable();
            } catch (Exception ex) {
                Messages.showErrorDialog("读取Excel文件失败: " + ex.getMessage(), "错误");
                ex.printStackTrace();
            }
        }
    }

    private void updatePreviewTable() {
        if (excelHeaders == null || excelData == null) {
            return;
        }
        
        tableModel = new DefaultTableModel();
        
        // Add headers
        for (String header : excelHeaders) {
            tableModel.addColumn(header != null ? header : "");
        }
        
        // Add data rows (limit to 10 for preview)
        int rowLimit = Math.min(excelData.size(), 10);
        for (int i = 0; i < rowLimit; i++) {
            List<String> row = excelData.get(i);
            if (row == null) {
                continue;
            }
            
            // Ensure row has correct number of columns
            Vector<String> tableRow = new Vector<>();
            for (int j = 0; j < excelHeaders.size(); j++) {
                if (j < row.size() && row.get(j) != null) {
                    tableRow.add(row.get(j));
                } else {
                    tableRow.add("");
                }
            }
            
            tableModel.addRow(tableRow);
        }
        
        previewTable.setModel(tableModel);
        
        // Adjust column widths
        for (int i = 0; i < previewTable.getColumnCount(); i++) {
            int width = 100; // Default width
            previewTable.getColumnModel().getColumn(i).setPreferredWidth(width);
        }
    }

    private void createMappingTable() {
        // Clear previous mappings
        mappingTableModel.setRowCount(0);
        columnMappings.clear();
        defaultValueMap.clear();
        
        if (selectedTable == null) {
            JOptionPane.showMessageDialog(this.getContentPane(), 
                "没有选择数据库表，请在数据库窗口中右键点击表选择Excel转SQL功能", 
                "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        // For each Excel header, create a row in the mapping table
        for (String header : excelHeaders) {
            Vector<Object> row = new Vector<>();
            row.add(header);
            
            // Try to find a match by name
            String matchedColumn = "";
            for (ColumnInfo info : dbColumnInfos) {
                if (info.getName().equalsIgnoreCase(header)) {
                    matchedColumn = info.getName();
                    break;
                }
            }
            
            if (!matchedColumn.isEmpty()) {
                columnMappings.put(header, matchedColumn);
            }
            
            // Add the row to the table model
            row.add(matchedColumn);
            
            // Set primary key checkbox
            boolean isPk = primaryKeyMap.getOrDefault(matchedColumn, false);
            row.add(isPk);
            
            // Add default value column (initially empty)
            row.add("");
            
            mappingTableModel.addRow(row);
        }
        
        // Set a combo box editor for target field column instead of SearchableColumnCellEditor
        mappingTable.getColumnModel().getColumn(1).setCellEditor(new ColumnComboBoxEditor(dbColumnInfos));
        
        // Set custom renderer for the target field column to show comment and name
        mappingTable.getColumnModel().getColumn(1).setCellRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                if (value instanceof ColumnInfo) {
                    value = ((ColumnInfo) value).toString();
                } else if (value != null && !value.toString().isEmpty()) {
                    // Find the column info to display comment with name
                    String colName = value.toString();
                    if (colName.contains("[") && colName.endsWith("]")) {
                        try {
                            colName = colName.substring(colName.lastIndexOf("[") + 1, colName.length() - 1);
                        } catch (IndexOutOfBoundsException e) {
                            // Keep original value
                        }
                    }
                    
                    for (ColumnInfo info : dbColumnInfos) {
                        if (info.getName().equals(colName)) {
                            value = info.toString();
                            break;
                        }
                    }
                }
                return super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
            }
        });
        
        // Add listeners to update mappings when changed
        mappingTable.getModel().addTableModelListener(e -> {
            if (e.getFirstRow() < 0 || e.getFirstRow() >= mappingTable.getRowCount()) {
                return; // Invalid row index
            }
            
            if (e.getColumn() == 1) { // Target field column
                String source = (String) mappingTable.getValueAt(e.getFirstRow(), 0);
                if (source == null || source.isEmpty()) {
                    return;
                }
                
                Object targetObj = mappingTable.getValueAt(e.getFirstRow(), 1);
                String target = "";
                
                if (targetObj instanceof ColumnInfo) {
                    target = ((ColumnInfo) targetObj).getName();
                } else if (targetObj != null) {
                    target = targetObj.toString();
                    
                    // If it's in format "comment [name]", extract just the name
                    if (target.contains("[") && target.endsWith("]")) {
                        try {
                            target = target.substring(target.lastIndexOf("[") + 1, target.length() - 1);
                        } catch (IndexOutOfBoundsException ex) {
                            // Keep original value
                        }
                    }
                }
                
                if (!target.isEmpty()) {
                    columnMappings.put(source, target);
                } else {
                    columnMappings.remove(source);
                }
            } else if (e.getColumn() == 2) { // Primary key column
                String target = getColumnNameFromCell(mappingTable, e.getFirstRow(), 1);
                if (target == null || target.isEmpty()) {
                    return;
                }
                
                Boolean isPk = (Boolean) mappingTable.getValueAt(e.getFirstRow(), 2);
                if (isPk != null) {
                    primaryKeyMap.put(target, isPk);
                }
            } else if (e.getColumn() == 3) { // Default value column
                String target = getColumnNameFromCell(mappingTable, e.getFirstRow(), 1);
                if (target == null || target.isEmpty()) {
                    return;
                }
                
                String defaultValue = (String) mappingTable.getValueAt(e.getFirstRow(), 3);
                if (defaultValue != null && !defaultValue.isEmpty()) {
                    defaultValueMap.put(target, defaultValue);
                } else {
                    defaultValueMap.remove(target);
                }
            }
        });
    }
    
    private String getColumnNameFromCell(JTable table, int row, int column) {
        Object value = table.getValueAt(row, column);
        if (value == null) return "";
        
        if (value instanceof ColumnInfo) {
            return ((ColumnInfo) value).getName();
        }
        
        String strValue = value.toString();
        if (strValue.contains("[") && strValue.endsWith("]")) {
            try {
                return strValue.substring(strValue.lastIndexOf("[") + 1, strValue.length() - 1);
            } catch (IndexOutOfBoundsException e) {
                // In case of malformed string
                return strValue;
            }
        }
        
        return strValue;
    }
    
    private void onAddMapping(ActionEvent e) {
        // Add a new empty row to the mapping table
        Vector<Object> row = new Vector<>();
        row.add(""); // Empty source field (user will type in)
        row.add(""); // Empty target field
        row.add(false); // Not a primary key
        row.add(""); // No default value
        
        mappingTableModel.addRow(row);
    }
    
    private void onRemoveMapping(ActionEvent e) {
        int selectedRow = mappingTable.getSelectedRow();
        if (selectedRow >= 0) {
            // Remove the mapping from our data structures
            String source = (String) mappingTable.getValueAt(selectedRow, 0);
            String target = getColumnNameFromCell(mappingTable, selectedRow, 1);
            
            if (source != null && !source.isEmpty()) {
                columnMappings.remove(source);
            }
            
            if (target != null && !target.isEmpty()) {
                primaryKeyMap.remove(target);
                defaultValueMap.remove(target);
            }
            
            // Remove the row from the table
            mappingTableModel.removeRow(selectedRow);
        } else {
            JOptionPane.showMessageDialog(this.getContentPane(), 
                "请先选择要删除的映射行", "提示", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    private void onGenerateSql(ActionEvent e) {
        if (selectedTable == null) {
            Messages.showErrorDialog("请先选择数据库表", "错误");
            return;
        }
        
        if (excelHeaders == null || excelHeaders.isEmpty()) {
            Messages.showErrorDialog("请先选择Excel文件", "错误");
            return;
        }
        
        // 首先清理以前的手动映射占位符
        List<String> manualPlaceholders = new ArrayList<>();
        for (String key : columnMappings.keySet()) {
            if (key.startsWith("MANUAL_")) {
                manualPlaceholders.add(key);
            }
        }
        for (String placeholder : manualPlaceholders) {
            columnMappings.remove(placeholder);
        }
        
        // 获取手动映射（没有Excel源字段的映射）
        List<String> manualHeaders = new ArrayList<>();
        
        for (int i = 0; i < mappingTable.getRowCount(); i++) {
            String source = (String) mappingTable.getValueAt(i, 0);
            if (source == null || source.isEmpty()) {
                String target = getColumnNameFromCell(mappingTable, i, 1);
                if (target == null || target.isEmpty()) {
                    continue;
                }
                
                String defaultValue = (String) mappingTable.getValueAt(i, 3);
                
                // 对于没有Excel源的手动映射，创建一个唯一的占位符
                String placeholder = "MANUAL_" + i;
                columnMappings.put(placeholder, target);
                manualHeaders.add(placeholder);
                
                if (defaultValue != null && !defaultValue.isEmpty()) {
                    defaultValueMap.put(target, defaultValue);
                }
                
                // 检查是否为主键
                Boolean isPk = (Boolean) mappingTable.getValueAt(i, 2);
                if (isPk != null && isPk) {
                    primaryKeyMap.put(target, true);
                }
            } else {
                // 对于有Excel源的映射，也需要检查是否有默认值
                String target = getColumnNameFromCell(mappingTable, i, 1);
                if (target != null && !target.isEmpty()) {
                    String defaultValue = (String) mappingTable.getValueAt(i, 3);
                    if (defaultValue != null && !defaultValue.isEmpty()) {
                        defaultValueMap.put(target, defaultValue);
                    }
                }
            }
        }
        
        try {
            String sqlType = (String) sqlTypeComboBox.getSelectedItem();
            if (sqlType == null) {
                sqlType = "INSERT"; // 默认使用INSERT
            }
            
            String tableName = selectedTable.getName();
            
            // 获取主键列
            List<String> primaryKeys = new ArrayList<>();
            for (Map.Entry<String, Boolean> entry : primaryKeyMap.entrySet()) {
                if (entry.getValue() != null && entry.getValue() && entry.getKey() != null && !entry.getKey().isEmpty()) {
                    primaryKeys.add(entry.getKey());
                }
            }
            
            // INSERT时使用模板模式
            boolean templateOnly = "INSERT".equals(sqlType) && templateOnlyCheckBox.isSelected();
            
            // 合并Excel头和手动添加的列
            List<String> allHeaders = new ArrayList<>();
            if (excelHeaders != null) {
                allHeaders.addAll(excelHeaders);
            }
            allHeaders.addAll(manualHeaders);
            
            // 检查是否有有效的列映射
            if (columnMappings.isEmpty()) {
                Messages.showErrorDialog("没有有效的列映射，请先设置字段映射", "错误");
                return;
            }
            
            // 准备SQL生成
            boolean hasDataRows = excelData != null && !excelData.isEmpty();
            
            // 生成SQL
            String sql;
            
            if (templateOnly && "INSERT".equals(sqlType)) {
                // 模板模式下，直接创建一个带默认值的INSERT语句
                StringBuilder columns = new StringBuilder();
                StringBuilder values = new StringBuilder();
                
                List<String> columnNames = new ArrayList<>();
                
                // 收集所有列名
                for (String header : allHeaders) {
                    String columnName = columnMappings.get(header);
                    if (columnName != null && !columnName.isEmpty()) {
                        columnNames.add(columnName);
                    }
                }
                
                // 添加列和值
                for (int i = 0; i < columnNames.size(); i++) {
                    String columnName = columnNames.get(i);
                    
                    // 添加列名
                    if (i > 0) columns.append(", ");
                    columns.append(columnName);
                    
                    // 添加值（使用默认值或列名作为占位符）
                    if (i > 0) values.append(", ");
                    String defaultValue = defaultValueMap.get(columnName);
                    if (defaultValue != null && !defaultValue.isEmpty()) {
                        values.append("'").append(defaultValue).append("'");
                    } else {
                        values.append("'").append(columnName).append("'");
                    }
                }
                
                sql = "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + values + ");";
            } else {
                // 使用通用SQL生成器
                sql = SqlGeneratorUtil.generateSql(sqlType, tableName, 
                        allHeaders, 
                        excelData, columnMappings, primaryKeys, false, templateOnly);
                
                // 在非模板模式下，尝试在SQL文本中替换默认值
                if (templateOnly && "INSERT".equals(sqlType)) {
                    for (Map.Entry<String, String> entry : defaultValueMap.entrySet()) {
                        String columnName = entry.getKey();
                        String defaultValue = entry.getValue();
                        if (columnName != null && !columnName.isEmpty() && defaultValue != null) {
                            // 将列名替换为默认值
                            sql = sql.replace("'" + columnName + "'", "'" + defaultValue + "'");
                        }
                    }
                }
            }
            
            sqlPreviewArea.setText(sql);
        } catch (Exception ex) {
            Messages.showErrorDialog("生成SQL失败: " + ex.getMessage(), "错误");
            ex.printStackTrace(); // 打印堆栈跟踪以便调试
        }
    }

    private void onCopySql(ActionEvent e) {
        String sql = sqlPreviewArea.getText();
        if (sql == null || sql.isEmpty()) {
            showWarningMessage("请先生成SQL");
            return;
        }
        
        StringSelection selection = new StringSelection(sql);
        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        clipboard.setContents(selection, selection);
        
        showInfoMessage("SQL已复制到剪贴板");
    }

    private void onExecuteSql(ActionEvent e) {
        String sql = sqlPreviewArea.getText();
        if (sql == null || sql.isEmpty()) {
            showWarningMessage("请先生成SQL");
            return;
        }
        
        // TODO: 实现SQL执行功能
        showInfoMessage("SQL执行功能尚未实现");
    }

    // 辅助方法：显示错误消息
    private void showErrorMessage(String message) {
        Messages.showErrorDialog(message, "错误");
    }

    // 辅助方法：显示警告消息
    private void showWarningMessage(String message) {
        Messages.showWarningDialog(message, "警告");
    }

    // 辅助方法：显示信息消息
    private void showInfoMessage(String message) {
        Messages.showInfoMessage(message, "提示");
    }

    @Override
    protected @Nullable JComponent createCenterPanel() {
        return mainPanel;
    }
} 