package club.bigtian.dialogs;

import club.bigtian.constant.SysConstant;
import club.bigtian.dto.*;
import club.bigtian.entity.InternationalExport;
import club.bigtian.entity.RoleExport;
import club.bigtian.entity.RolePersonExport;
import club.bigtian.handler.ExcelDicHandler;
import club.bigtian.persistent.PluginSettings;
import club.bigtian.render.MyComboBoxEditor;
import club.bigtian.render.MyComboBoxRenderer;
import club.bigtian.tool.FlowEnvironmentConfigDialog;
import club.bigtian.tool.InterntionConfigDialog;
import club.bigtian.util.*;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.BytesResource;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.intellij.icons.AllIcons;
import com.intellij.ide.fileTemplates.impl.UrlUtil;
import com.intellij.ide.highlighter.JavaFileType;
import com.intellij.ide.util.TreeClassChooser;
import com.intellij.ide.util.TreeClassChooserFactory;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.fileChooser.FileChooser;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.ide.CopyPasteManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.ComboBox;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.FixedSizeButton;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.*;
import com.intellij.psi.codeStyle.CodeStyleManager;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.ui.components.fields.ExpandableTextField;
import com.intellij.ui.components.fields.ExtendableTextComponent;
import com.intellij.ui.components.fields.ExtendableTextField;
import com.intellij.ui.table.JBTable;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateBatchRequest;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateBatchResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import javax.swing.*;
import javax.swing.plaf.basic.BasicComboBoxEditor;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumn;
import javax.swing.table.TableModel;
import java.awt.*;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.StringSelection;
import java.awt.event.InputEvent;
import java.awt.event.ItemEvent;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.io.*;
import java.net.HttpCookie;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class WorkFlowDialog extends DialogWrapper {
    JButton button = new JButton("保存配置");
    JButton i8nBtn = new JButton("导出国际化文件");
    JButton otherI18nBtn = new JButton("其他国际化渲染");

    JButton dataSave = new JButton("暂存");
    JButton getData = new JButton("读取数据");
    JButton configI18n = new JButton("配置海外租户");
    JButton configEnv = new JButton("配置环境");
    JButton uploadBtn = new JButton("上传流程文件");
    JButton expRole = new JButton("导出角色人员");
    JLabel classLabel = new JLabel("类名：");
    JLabel countLabel = new JLabel();
    JLabel processNameLabel = new JLabel();
    JTextField classField = new JTextField();
    JTextField tenantField = new JTextField();
    JButton tenantBtn = new JButton("一键修改租户");
    JComboBox<FlowEnvironment> envComboBox = new JComboBox<>();
    JBTable table;
    JPanel tenantPanel;
    private AnActionEvent anActionEvent;
    private Map<String, NodeItem> nodeItemMap;
    private Map<String, String> type = new HashMap<>() {{
        put("角色", "role");
        put("部门", "dept");
        put("人员", "user");
    }};
    private Map<String, String> codeType = new HashMap<>() {{
        put("role", "角色");
        put("dept", "部门");
        put("user", "人员");
    }};
    Object[][] data;
    String[] columnNames = {"环节名称", "环节ID", "环节类型", "部门", "角色编码","角色名称", "人员", "下一环节"};
    Map<String, NodeItem> nodeIdNameMap;
    List<Node> nodeList;
    List<String> nodeItemList;
    List<NodeItem> eventList;

    ComboBox classComboBox;

    List<File> fileList;

    private int index = 1;

    // 使用嵌套Map保存每行/每个角色编码对应的角色列表
    private final Map<Integer, Map<String, List<RoleDTO>>> rowRoleCodeMap = new HashMap<>();
    // 保存每行已选择的角色名称
    private final Map<Integer, String> selectedRoleNames = new HashMap<>();

    public WorkFlowDialog(AnActionEvent anActionEvent) {
        super(true);
        setTitle("流程环节配置");
        this.nodeList = showFileChooser(anActionEvent.getProject());
        setData(nodeList);
        this.anActionEvent = anActionEvent;
        init();
        setTitle("流程环节配置");
        setSize(1500, 600);
    }

    public List<Node> showFileChooser(Project project) {
        fileList = new ArrayList<>();
        FileChooserDescriptor descriptor =
                new FileChooserDescriptor(true, false, false, false, false, true);
        VirtualFile[] virtualFiles = FileChooser.chooseFiles(descriptor, project, null);
        List<Node> nodes = new ArrayList<>();
        for (VirtualFile file : virtualFiles) {
            // 规范化路径
            File file1 = new File(file.getPath());
            fileList.add(file1);
            nodes.addAll(analysisFile(file1));
        }
        return nodes;

    }


    public List<Node> analysisFile(File file) {
        List<Node> list = new ArrayList<>();

        try {
            Map<String, FastByteArrayOutputStream> zipData = WorkFlowDialog.zipData(IoUtil.toStream(file));
            for (Map.Entry<String, FastByteArrayOutputStream> entry : zipData.entrySet()) {
                SAXReader saxReader = new SAXReader();
                org.dom4j.Document document = saxReader.read(new ByteArrayInputStream(entry.getValue().toByteArray()));
                Element rootElement = document.getRootElement();
                Element process = rootElement.element("process");
                List<NodeItem> eventList = new ArrayList<>();
                Node node = new Node();
                node.setProcessDefKey(process.attributeValue("id"));
                node.setProcessDefName(process.attributeValue("name"));
                node.setEventList(eventList);
                list.add(node);
                Element startEvent = process.element("startEvent");
                Element endEvent = process.element("endEvent");
                startEvent.addAttribute("name", "开始");
                endEvent.addAttribute("name", "结束");
                List<Element> sequenceFlow = new ArrayList<>(process.elements("sequenceFlow"));
                LinkedList<NodeItem> linkedList = new LinkedList<>();
                Map<String, List<String>> listMap = sequenceFlow.stream()
                        .collect(Collectors.groupingBy(element -> element.attributeValue("sourceRef"), Collectors.mapping(element -> element.attributeValue("targetRef"), Collectors.toList())));
                listMap.values()
                        .stream()
                        .forEach(el -> {
                            if (el.size() > 1 && el.contains("end")) {
                                el.remove("end");
                            }
                        });
                listMap = new ConcurrentHashMap<>(listMap);
                String startId = startEvent.attributeValue("id");
                getNextNode(startId, listMap, linkedList, process);
                node.setUserTaskList(linkedList);
            }
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        }

        return list;

    }


    /**
     * 得到下一个节点
     *
     * @param startId    开始id
     * @param listMap    地图列表
     * @param linkedList 链表
     * @param process    过程
     */
    private void getNextNode(String startId, Map<String, List<String>> listMap, LinkedList<NodeItem> linkedList, Element process) {

        listMap.keySet().forEach(el -> {
            List<String> nodeList = listMap.get(startId);
            if (CollUtil.isEmpty(nodeList)) {
                return;
            }
            for (String nodeKey : nodeList) {
                boolean empty = linkedList.stream()
                        .filter(nodeItem -> nodeItem.getActivityDefId().equals(nodeKey))
                        .findFirst().isEmpty();

                if (empty) {
                    String xpathExpression = "//*[@id='" + nodeKey + "']";
                    Element element = (Element) process.selectSingleNode(xpathExpression);
                    String id = element.attributeValue("id");
                    NodeItem nodeItem = new NodeItem();
                    nodeItem.setActivityDefId(id);
                    nodeItem.setActivityDefIdCame(StrUtil.toCamelCase(id));
                    nodeItem.setActivityDefIdFinal(StrUtil.swapCase(id));
                    nodeItem.setActivityDefName(element.attributeValue("name"));
                    List<String> list = listMap.get(nodeKey);
                    if (CollUtil.isNotEmpty(list)) {
                        List<NodeItem> nextNodeItemList = list.stream()
                                .map(e -> {
                                    NodeItem item = new NodeItem();
                                    item.setActivityDefId(e);
                                    item.setActivityDefName(process.selectSingleNode("//*[@id='" + e + "']")
                                            .valueOf("@name"));
                                    return item;
                                })
                                .collect(Collectors.toList());
                        nodeItem.setNextNodeList(nextNodeItemList);
                        nodeItem.setNextActivityDefId(list.get(0));
                        nodeItem.setNextActivityDefIdFinal(StrUtil.swapCase(nodeItem.getNextActivityDefId()));
                    }

                    linkedList.add(nodeItem);
                }
                listMap.remove(startId);
                getNextNode(nodeKey, listMap, linkedList, process);
            }
        });
    }


    /**
     * 获取租户
     *
     * @param stream
     * @return
     * @time 2023/5/23 17:31
     * @since 1.0
     */
    public static Map<String, String> getTenant(InputStream stream) {
        ZipInputStream zipInputStream = new ZipInputStream(stream);
        Map<String, String> data = new HashMap<>();
        ZipEntry entry;
        try {
            while (true) {
                if ((entry = zipInputStream.getNextEntry()) == null) {
                    break;
                }
                String name = entry.getName();
                if (!name.contains("data")) {
                    continue;
                }
                FastByteArrayOutputStream read = IoUtil.read(zipInputStream, false);
                String info = read.toString();

                String tenantId = ReUtil.getGroup1(Pattern.compile("<tenantId>(.*?)</tenantId>"), info);
                System.out.println(tenantId);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return data;
    }


    public void setData(List<Node> list) {
        // list转二维数组
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Node node = list.get(0);
        classField.setText(StrUtil.upperFirst(StrUtil.toCamelCase(node.getProcessDefKey())) + "WorkFlow");
        eventList = node.getUserTaskList();
        nodeItemList = eventList.stream().map(NodeItem::getActivityDefName).collect(Collectors.toList());
        nodeIdNameMap = eventList.stream()
                .collect(Collectors.toMap(NodeItem::getActivityDefId, el -> el));
        nodeItemMap = eventList.stream()
                .collect(Collectors.toMap(NodeItem::getActivityDefName, el -> el));
        eventList = eventList.stream()
                .filter(nodeItem -> !nodeItem.getActivityDefId().startsWith("sid") && !"end".equalsIgnoreCase(nodeItem.getActivityDefId()))
                .collect(Collectors.toList());
        initTableData();

        // 更新流程名称标签
        updateProcessNameLabel(node.getProcessDefName());
    }


    public void setData(int idx) {
        getTableData();

        Node node = nodeList.get(idx);
        classField.setText(StrUtil.upperFirst(StrUtil.toCamelCase(node.getProcessDefKey())) + "WorkFlow");
        eventList = node.getUserTaskList();
        nodeItemList = eventList.stream().map(NodeItem::getActivityDefName).collect(Collectors.toList());
        nodeIdNameMap = eventList.stream()
                .collect(Collectors.toMap(NodeItem::getActivityDefId, el -> el));
        nodeItemMap = eventList.stream()
                .collect(Collectors.toMap(NodeItem::getActivityDefName, el -> el));
        eventList = eventList.stream()
                .filter(nodeItem -> !nodeItem.getActivityDefId().startsWith("sid") && !"end".equalsIgnoreCase(nodeItem.getActivityDefId()))
                .collect(Collectors.toList());

        initTableData();
        table.setModel(getModel());
        table.repaint();

        // 更新流程名称标签
        updateProcessNameLabel(node.getProcessDefName());
    }

    private void initTableData() {
        data = new Object[eventList.size()][];
        for (int i = 0; i < eventList.size(); i++) {
            NodeItem node = eventList.get(i);
            data[i] = new Object[]{
                    node.getActivityDefName(),
                    node.getActivityDefId(),
                    ObjectUtil.isNotNull(node.getType()) ? codeType.get(node.getType()) : "角色",
                    ObjectUtil.defaultIfNull(node.getDeptCode(), ""),
                    ObjectUtil.defaultIfNull(node.getRoleCode(), ""),
                    ObjectUtil.defaultIfNull(node.getRoleName(), ""),
                    ObjectUtil.defaultIfNull(node.getReceive(), ""),
                    nodeIdNameMap.get(node.getNextActivityDefId()).getActivityDefName()
            };
        }
    }

    private void readTableData() {
        data = new Object[eventList.size()][];
        for (int i = 0; i < eventList.size(); i++) {
            NodeItem node = eventList.get(i);
            String nodeType = "";
            for (Map.Entry<String, String> entry : type.entrySet()) {
                if (ObjectUtil.isNull(node.getType())) {
                    nodeType = "角色";
                }
                if (entry.getValue().equals(node.getType())) {
                    nodeType = entry.getKey();
                }
            }
            NodeItem nodeItem = nodeIdNameMap.get(node.getNextActivityDefId());
            if (ObjectUtil.isNull(nodeItem)) {
                NotificationUtils.notifyError(StrUtil.format("没有该当前环节:{}", node.getNextActivityDefId()), "提示", anActionEvent.getProject());
                return;
            }
            data[i] = new Object[]{
                    node.getActivityDefName(),
                    node.getActivityDefId(),
                    nodeType,
                    node.getDeptCode(),
                    node.getRoleCode(),
                    node.getRoleName(),
                    node.getReceive(),
                    nodeItem.getActivityDefName()
            };
        }
    }

    public JComboBox<String> createNodeBox(List<String> nodeNameList, JTable table) {
        // 创建下拉框渲染器

        FilterComboBoxModel model = new FilterComboBoxModel(nodeNameList);
        JComboBox<String> comboBox = new JComboBox<>(model);
        comboBox.setEditable(true);

        JTextField filterTextField = (JTextField) comboBox.getEditor().getEditorComponent();
        filterTextField.addKeyListener(new KeyListener() {
            @Override
            public void keyTyped(KeyEvent e) {
            }

            @Override
            public void keyPressed(KeyEvent e) {
                int keyCode = e.getKeyCode();
                if (e.isControlDown() && keyCode == KeyEvent.VK_V || e.isMetaDown() && keyCode == KeyEvent.VK_META) {
                    String filterText = filterTextField.getText();
                    SwingUtilities.invokeLater(() -> comboBox.getEditor().setItem(filterText));
                    model.filterItems(filterText);
                    comboBox.hidePopup();
                    comboBox.showPopup();
                }

            }

            @Override
            public void keyReleased(KeyEvent e) {
                int keyCode = e.getKeyCode();
                if (
                        keyCode == KeyEvent.VK_SPACE || keyCode == KeyEvent.VK_DELETE || keyCode ==
                                KeyEvent.VK_BACK_SPACE || (e.getModifiers() & InputEvent.CTRL_MASK) != 0 && keyCode ==
                                KeyEvent.VK_V || keyCode > 48 && keyCode <= 57 || (e.isControlDown() || e.isMetaDown()) && keyCode == KeyEvent.VK_V) {
                    String filterText = filterTextField.getText();
                    SwingUtilities.invokeLater(() -> comboBox.getEditor().setItem(filterText));
                    model.filterItems(filterText);
                    comboBox.hidePopup();
                    comboBox.showPopup();
                }
            }
        });


        comboBox.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                Object selectedItem = comboBox.getSelectedItem();
                if (selectedItem != null) {
                    filterTextField.setText(selectedItem.toString());
                    model.filterItems("");
                }
            }
        });

        TableColumn comboBoxColumn = table.getColumnModel().getColumn(7);
        comboBoxColumn.setCellRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                return this;
            }
        });
        comboBoxColumn.setCellEditor(new DefaultCellEditor(comboBox));
        // 设置下拉框的显示值宽度
        comboBox.setPrototypeDisplayValue("角色");
        comboBoxColumn.setCellEditor(new DefaultCellEditor(comboBox));
        return comboBox;
    }

    private static class FilterComboBoxModel extends AbstractListModel<String> implements ComboBoxModel<String> {
        private List<String> items;
        private List<String> filteredItems;
        private String selectedItem;

        public FilterComboBoxModel(List<String> items) {
            this.items = items;
            this.filteredItems = new ArrayList<>(items);
        }

        public void filterItems(String filterText) {
            filteredItems.clear();
            for (String item : items) {
                if (item.toLowerCase().contains(filterText.toLowerCase())) {
                    filteredItems.add(item);
                }
            }
            fireContentsChanged(this, 0, getSize());
        }

        @Override
        public int getSize() {
            return filteredItems.size();
        }

        @Override
        public String getElementAt(int index) {
            return filteredItems.get(index);
        }

        @Override
        public void setSelectedItem(Object anItem) {
            selectedItem = (String) anItem;
        }

        @Override
        public Object getSelectedItem() {
            return selectedItem;
        }
    }

    public String treeClass() {
        Project project = anActionEvent.getProject();
        TreeClassChooserFactory factory = TreeClassChooserFactory.getInstance(project);
        final com.intellij.openapi.module.Module module = null;
        GlobalSearchScope scope = null;
        if (module != null) {
            scope = GlobalSearchScope.moduleWithDependenciesAndLibrariesScope(module);
        } else {
            scope = GlobalSearchScope.allScope(project);
        }
        PsiClass ecClass = JavaPsiFacade.getInstance(project).findClass("", scope);
        TreeClassChooser chooser = factory.createInheritanceClassChooser("选择常量类", scope, ecClass, null);
        chooser.showDialog();
        PsiClass selected = chooser.getSelected();
        return selected.getQualifiedName();
    }

    public ComboBox getBox() {

        ComboBox<String> eComboBox = new ComboBox<>();
        eComboBox.setEditable(true);
        eComboBox.setPreferredSize(new Dimension(200, eComboBox.getPreferredSize().height));
        ExtendableTextComponent.Extension browseExtension =
                ExtendableTextComponent.Extension.create(AllIcons.Actions.Search, AllIcons.Actions.Search,
                        "搜索类", () -> {
                            String treeClass = treeClass();
                            eComboBox.getEditor().setItem(treeClass);
                        });
        ExpandableTextField expandableTextField = new ExpandableTextField();
        expandableTextField.addExtension(browseExtension);
        eComboBox.setEditor(new BasicComboBoxEditor() {
            @Override
            protected JTextField createEditorComponent() {
                ExtendableTextField ecbEditor = new ExtendableTextField();
                ecbEditor.addExtension(browseExtension);
                ecbEditor.setBorder(null);
                return ecbEditor;
            }
        });


        PluginSettings pluginSettings = PluginSettings.getInstance();
        String pluginSettingJson = pluginSettings.getState().pluginWorkFlowJson;
        if (StrUtil.isNotBlank(pluginSettingJson)) {
            String className = JSONUtil.parseObj(pluginSettingJson).getStr(SysConstant.WORK_FLOW_CONSTANT);
            eComboBox.getEditor().setItem(className);
        }
        return eComboBox;
    }


    @Nullable
    @Override
    public JComponent createCenterPanel() {
        Project project = anActionEvent.getProject();
        JPanel panel = new JPanel();
        panel.setLayout(new BorderLayout());

        JPanel panelTool = new JPanel();
        JLabel label = new JLabel("常量类：");
        panelTool.setLayout(new BorderLayout());

        // 创建顶部面板放置必要的输入控件
        JPanel topPanel = new JPanel();
        topPanel.setLayout(new BoxLayout(topPanel, BoxLayout.X_AXIS));
        topPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        // 添加环境选择下拉框
        JLabel envLabel = new JLabel("环境：");
        topPanel.add(envLabel);
        topPanel.add(Box.createRigidArea(new Dimension(5, 0)));

        // 加载环境配置
        loadEnvironments();
        envComboBox.setPreferredSize(new Dimension(150, envComboBox.getPreferredSize().height));
        topPanel.add(envComboBox);
        topPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        topPanel.add(configEnv);
        topPanel.add(Box.createRigidArea(new Dimension(10, 0)));

        // 设置环境切换监听器
        envComboBox.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                saveCurrentEnvironment();
            }
        });

        // 设置配置环境按钮监听器
        configEnv.addActionListener(e -> {
            new FlowEnvironmentConfigDialog(anActionEvent).show();
            loadEnvironments(); // 重新加载环境配置
        });

        topPanel.add(label);
        topPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        classComboBox = getBox();
        topPanel.add(classComboBox);
        topPanel.add(Box.createRigidArea(new Dimension(10, 0)));

        classField.setPreferredSize(new Dimension(200, classField.getPreferredSize().height));
        tenantField.setPreferredSize(new Dimension(100, classField.getPreferredSize().height));
        topPanel.add(classLabel);
        topPanel.add(Box.createRigidArea(new Dimension(5, 0)));

        topPanel.add(classField);
        topPanel.add(Box.createRigidArea(new Dimension(10, 0)));
        JLabel tenantLabel = new JLabel("租户：");
        topPanel.add(tenantLabel);
        topPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        topPanel.add(tenantField);
        // 添加弹性空间，让控件占满一行
        topPanel.add(Box.createHorizontalGlue());

        // 创建底部面板放置按钮
        JPanel buttonPanel = new JPanel();
        buttonPanel.setLayout(new BoxLayout(buttonPanel, BoxLayout.X_AXIS));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        buttonPanel.add(tenantBtn);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(button);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(i8nBtn);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(otherI18nBtn);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(dataSave);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(getData);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(configI18n);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(uploadBtn);
        buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));
        buttonPanel.add(expRole);
        // 添加弹性空间，让控件占满一行
        buttonPanel.add(Box.createHorizontalGlue());

        // 将两个面板添加到panelTool
        panelTool.add(topPanel, BorderLayout.NORTH);
        panelTool.add(buttonPanel, BorderLayout.CENTER);

        panel.add(panelTool, BorderLayout.NORTH);

        // 暂存
        PluginSettings instance = PluginSettings.getInstance();
        dataSave.addActionListener(e -> {
            getTableData();
            PluginSettings.State state = instance.getState();
            state.pluginWorkFlowData = JSONUtil.toJsonStr(nodeList);
            instance.loadState(state);
            getData.setEnabled(true);
            NotificationUtils.notifySuccess("数据暂存成功", "提示", anActionEvent.getProject());
        });

        getData.addActionListener(e -> {
            PluginSettings.State state = instance.getState();
            String pluginWorkFlowData = state.pluginWorkFlowData;
            if (StrUtil.isNotEmpty(pluginWorkFlowData)) {
                nodeList = JSONUtil.toList(pluginWorkFlowData, Node.class);
                setData(nodeList);
                readTableData();
                getModel();
                NotificationUtils.notifySuccess("数据恢复成功", "提示", anActionEvent.getProject());
            }
        });

        setSection(getData);
        button.addActionListener(e -> {
            // 获取输入框内容
            String searchText = ((JTextField) classComboBox.getEditor().getEditorComponent()).getText();
            PluginSettings pluginSettings = instance;
            PluginSettings.State state = pluginSettings.getState();
            JSONObject settings = JSONUtil.createObj();
            settings.set(SysConstant.WORK_FLOW_CONSTANT, searchText);
            state.pluginWorkFlowJson = settings.toString();
            pluginSettings.loadState(state);
            NotificationUtils.notifySuccess("配置保存成功", "提示", anActionEvent.getProject());
        });

        configI18n.addActionListener(e -> {
            new InterntionConfigDialog(anActionEvent).setVisible(true);
            // 获取"国际租户"复选框
            Optional<JCheckBox> interCheckBox = Arrays.stream(tenantPanel.getComponents())
                    .filter(component -> component instanceof JCheckBox)
                    .map(component -> (JCheckBox) component)
                    .filter(checkbox -> "inter".equals(checkbox.getName()))
                    .findFirst();

            // 只有当选中了"国际租户"时才重新渲染
            if (interCheckBox.isPresent() && interCheckBox.get().isSelected()) {
                String pluginWorkInterTenant = instance.getState().pluginWorkInterTenant;
                if (StrUtil.isNotEmpty(pluginWorkInterTenant)) {
                    // 先清除所有选中状态
                    Arrays.stream(tenantPanel.getComponents())
                            .filter(component -> component instanceof JCheckBox)
                            .forEach(component -> ((JCheckBox) component).setSelected(false));

                    // 重新选中"国际租户"复选框
                    interCheckBox.get().setSelected(true);

                    // 根据保存的国际化租户重新选中checkbox
                    JSONUtil.parseArray(pluginWorkInterTenant).forEach(tenant -> {
                        Arrays.stream(tenantPanel.getComponents())
                                .filter(component -> component instanceof JCheckBox)
                                .filter(component -> component.getName().equals(tenant))
                                .forEach(component -> ((JCheckBox) component).setSelected(true));
                    });
                }
            }
        });

        i8nBtn.addActionListener(e -> {
            export(nodeList);
        });
        otherI18nBtn.addActionListener(e -> {
            CopyPasteManager manager = CopyPasteManager.getInstance();
            // 获取文本内容
            String clipboardText = manager.getContents(DataFlavor.stringFlavor);
            String[] lines = clipboardText.split("\n");

            Set<String> selectTenant = getSelectTenant();
            if(CollUtil.isEmpty(selectTenant)){
                Messages.showMessageDialog(
                        "请选择租户",// 弹框内容
                        "提示",   // 弹框标题
                        Messages.getErrorIcon() // 使用的信息图标
                );
                return ;
            }
            List<String> clipList = new ArrayList<>();

            if (clipboardText.contains("t_pf_dict")) {
                List<String> neededFields = CollUtil.newArrayList("DICT_PARENT_ID", "DICT_CODE", "DICT_NAME", "STATUS", "SORT");
                clipList = extractFieldsFromSql(clipboardText, neededFields, selectTenant);
            } else if (clipboardText.contains("t_crm_busiconf")) {
                List<String> neededFields = CollUtil.newArrayList("busi_name", "busi_key", "busi_type", "busi_value", "busi_desc");
                clipList = extractFieldsFromSql(clipboardText, neededFields, selectTenant);
            } else if (clipboardText.contains("t_crm_code_mapping")) {
                List<String> neededFields = CollUtil.newArrayList("busi_type", "busi_name", "busi_code", "crm_code", "code_name","sys_code");
                clipList = extractFieldsFromSql(clipboardText, neededFields, selectTenant);
            } else {
                for (String tenantId : selectTenant) {
                    for (String line : lines) {
                        if (line.contains("%s")) {
                            clipList.add(String.format(line, tenantId));
                        } else {
                            clipList.add(line + "\t" + tenantId);
                        }
                    }
                }
            }


            setClipboardText( CollUtil.join(clipList, "\n"));
            Messages.showMessageDialog("转换成功","提示", Messages.getInformationIcon());
        });

        uploadBtn.addActionListener(e -> {
            try {
                uploadFlowFile();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        });

        expRole.addActionListener(e -> {
            expRoleFile();
        });


        tenantBtn.addActionListener(e -> {
            Set<String> selectTenant = getSelectTenant();
            if (StrUtil.isNotBlank(tenantField.getText())) {
                selectTenant.addAll(Arrays.asList(tenantField.getText().split(",")));
            }

            // 先让用户选择一次保存位置
            FileChooserDescriptor descriptor =
                    new FileChooserDescriptor(false, true, false, false, false, false);
            VirtualFile virtualFile = FileChooser.chooseFile(descriptor, project, null);

            if (virtualFile == null) {
                return; // 用户取消了操作
            }
            Map<String, String> flowTenantMap = getFlowTenantMap();
            Map<String, List<File>> fileMap = new HashMap<>();

            for (File file : fileList) {
                Map<String, FastByteArrayOutputStream> zipData = unzipData(IoUtil.toStream(file));

                // 获取原始文件名(不带扩展名)，用于区分不同源文件的导出
                String sourceFileName = FileUtil.mainName(file).split("-")[0];
                
                for (Map.Entry<String, FastByteArrayOutputStream> entry : zipData.entrySet()) {
                    String xmlVal = entry.getValue().toString();
                    for (String tenant : selectTenant) {
                        String currentTenant = entry.getKey().substring(0, entry.getKey().indexOf("."));
                        String suffix = entry.getKey().substring(entry.getKey().indexOf("."));
                        String proDefKey = StrUtil.subBefore(currentTenant, "_", true);
                        String newXmlVal = xmlVal;
                        if (suffix.contains("bpmn20")) {
                            newXmlVal = xmlVal.replaceAll(currentTenant, proDefKey + "_" + tenant);
                        }
                        //     生成文件，先放到内存然后zip导出
                        String userHome = System.getProperty("user.home");
                        File tempFile = FileUtil.file(userHome + File.separator + "workFlow" + File.separator + proDefKey + "_" + tenant + suffix);
                        FileUtil.writeString(newXmlVal, tempFile, "UTF-8");
                        fileMap.computeIfAbsent(tenant, k -> new ArrayList<>()).add(tempFile);
                    }
                }
                if (CollUtil.size(fileList) == 1) {
                    for (Map.Entry<String, List<File>> entry : fileMap.entrySet()) {
                        // 使用源文件名+租户名称作为导出文件名，避免覆盖
                        sourceFileName = originalName(sourceFileName, flowTenantMap);
                        String zipFileName = StrUtil.format("{}-{}-{}.zip", flowTenantMap.get(entry.getKey()), entry.getKey(), sourceFileName);
                        ZipUtil.zip(new File(virtualFile.getPath() + File.separator + zipFileName), false, entry.getValue().toArray(new File[0]));
                    }
                }
            }
            if (CollUtil.size(fileList) > 1) {
                for (Map.Entry<String, List<File>> entry : fileMap.entrySet()) {
                    // 使用源文件名+租户名称作为导出文件名，避免覆盖
                    String zipFileName = StrUtil.format("{}-{}个流程图-{}.zip", flowTenantMap.get(entry.getKey()), entry.getValue().size() / 2, entry.getKey());
                    ZipUtil.zip(new File(virtualFile.getPath() + File.separator + zipFileName), false, entry.getValue().toArray(new File[0]));
                }
            }
            // 删除临时文件
            fileMap.values()
                    .parallelStream()
                    .flatMap(Collection::stream)
                    .forEach(File::delete);
            Messages.showMessageDialog(
                    "导出成功", // 弹框内容
                    "提示",   // 弹框标题
                    Messages.getInformationIcon() // 使用的信息图标
            );
        });

        DefaultTableModel model = getModel();
        table = new JBTable(model);
        table.setRowHeight(30);
        createNodeBox(nodeItemList, table);

        // 添加表格焦点恢复机制，解决整个弹框失焦再聚焦的闪烁问题
        table.putClientProperty("terminateEditOnFocusLost", Boolean.TRUE);

        JScrollPane scrollPane = new JScrollPane(table);
        panel.add(scrollPane);
        Panel opPanel = new Panel();
        FixedSizeButton left = new FixedSizeButton();
        left.setIcon(AllIcons.General.ArrowLeft);
        left.addActionListener(e -> {
            if (index == 1) {
                return;
            }
            index--;
            setData(index - 1);
            table.repaint();
            countLabel.setText(StrUtil.format("{} / {} 个", index, nodeList.size()));
        });
        opPanel.add(left);

        FixedSizeButton rigth = new FixedSizeButton();
        rigth.setIcon(AllIcons.General.ArrowRight);
        rigth.addActionListener(e -> {
            if (index == nodeList.size()) {
                return;
            }
            index++;
            setData(index - 1);
            table.repaint();
            countLabel.setText(StrUtil.format("{} / {} 个", index, nodeList.size()));
        });
        opPanel.add(rigth);
        countLabel = new JLabel(StrUtil.format("{} / {} 个", index, nodeList.size()));
        opPanel.add(countLabel);

        // 添加流程名称标签到底部操作面板
        processNameLabel.setFont(new Font(processNameLabel.getFont().getName(), Font.BOLD, 14));
        processNameLabel.setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 0));
        opPanel.add(processNameLabel);

        // 初始化流程名称标签
        if (!nodeList.isEmpty()) {
            updateProcessNameLabel(nodeList.get(0).getProcessDefName());
        }

        tenantPanel = new JPanel();
        tenantPanel.setLayout(new BoxLayout(tenantPanel, BoxLayout.Y_AXIS));  // 设置为垂直布局

        tenantPanel.add(new JLabel("租户"));  // 添加"流程图"标签到左对齐面板
        JCheckBox checkBox = new JCheckBox("国际租户");
        checkBox.setName("inter");
        checkBox.addChangeListener(el -> {
            if (checkBox.isSelected()) {
                String pluginWorkInterTenant = instance.getState().pluginWorkInterTenant;
                JSONUtil.parseArray(pluginWorkInterTenant).forEach(el1 -> {
                    Arrays.stream(tenantPanel.getComponents())
                            .filter(component -> component instanceof JCheckBox)
                            .filter(component -> component.getName().equals(el1))
                            .forEach(component -> ((JCheckBox) component).setSelected(true));
                });
                return;
            }
            Arrays.stream(tenantPanel.getComponents())
                    .filter(component -> component instanceof JCheckBox)
                    .forEach(component -> ((JCheckBox) component).setSelected(false));
        });

        tenantPanel.add(checkBox);
        JSeparator separator = new JSeparator(SwingConstants.HORIZONTAL);
        separator.setMaximumSize(new Dimension(Integer.MAX_VALUE, 5)); // 设置最大高度为1像素
        tenantPanel.add(separator);

// 添加一个很小的垂直间距
        tenantPanel.add(Box.createRigidArea(new Dimension(0, 2))); // 只添加2像素的间距

        // tenantPanel.add(new JSeparator(SwingConstants.HORIZONTAL));
        // tenantPanel.add(Box.createRigidArea(new Dimension(0, 5))); // Add a small gap after the separator

        getApplicationList(tenantPanel);

        tenantPanel.setLayout(new BoxLayout(tenantPanel, BoxLayout.Y_AXIS)); // Ensure vertical alignment
        tenantPanel.setPreferredSize(new Dimension(200, tenantPanel.getComponentCount() * 30)); // Set preferred size based on the number of components

        JScrollPane jscrollPane = new JScrollPane(tenantPanel);
        jscrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        jscrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);

        panel.add(jscrollPane, BorderLayout.WEST);
        panel.add(opPanel, BorderLayout.SOUTH);

        // 创建类型选择下拉框
        ComboBox<BoxItem> box = new ComboBox<>();
        box.addItem(new BoxItem("角色", "role"));
        box.addItem(new BoxItem("部门", "dept"));
        box.addItem(new BoxItem("人员", "user"));
        box.setRenderer(new MyComboBoxRenderer());
        
        TableColumn comboBoxColumn = table.getColumnModel().getColumn(2);
        comboBoxColumn.setCellEditor(new MyComboBoxEditor(box));

        // 初始化表格编辑器和渲染器
        initTableEditors();

        return panel;
    }

    public static String convertSqlToSingleLine(String multiLineSql) {
        if (multiLineSql == null || multiLineSql.trim().isEmpty()) {
            return "";
        }

        // 去除所有换行符
        String singleLine = multiLineSql.replaceAll("\\r\\n|\\r|\\n", " ");

        // 处理JSON中的空格，保留JSON的结构但去除多余空格
        StringBuilder result = new StringBuilder();
        boolean inJson = false;
        int bracketCount = 0;

        for (int i = 0; i < singleLine.length(); i++) {
            char c = singleLine.charAt(i);

            // 检测是否在JSON字符串内
            if (c == '{') {
                inJson = true;
                bracketCount++;
                result.append(c);
            } else if (c == '}') {
                bracketCount--;
                if (bracketCount == 0) {
                    inJson = false;
                }
                result.append(c);
            } else if (inJson && Character.isWhitespace(c)) {
                // 在JSON内部时，只保留必要的空格
                if (i > 0 && !Character.isWhitespace(singleLine.charAt(i - 1))
                        && i < singleLine.length() - 1 && !Character.isWhitespace(singleLine.charAt(i + 1))) {
                    result.append(c);
                }
            } else {
                result.append(c);
            }
        }

        // 合并多个连续空格为单个空格
        return result.toString().replaceAll("\\s+", " ").trim();
    }

    public static List<String> extractFieldsFromSql(String sqlText, List<String> neededFields, Set<String> selectTenant) {
        // 定义要提取的字段

        // 分割多个SQL语句 - 查找所有INSERT语句
        Pattern sqlPattern = Pattern.compile("INSERT INTO.*?VALUES\\s*\\((.*?)\\);", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        List<String> sqlStatements = ReUtil.findAll(sqlPattern, sqlText, 0);

        List<Map<String, String>> allResults = new ArrayList<>();
        Pattern fieldsPattern = Pattern.compile("INSERT INTO .*?\\((.*?)\\)", Pattern.DOTALL);
        Pattern valuesPattern = Pattern.compile("VALUES\\s*\\((.*?)\\);", Pattern.DOTALL);

        for (String statement : sqlStatements) {
            // 提取字段名
            String fieldsStr = ReUtil.getGroup1(fieldsPattern, statement);
            if (fieldsStr == null || fieldsStr.isEmpty()) {
                continue;
            }
            List<String> fields = new ArrayList<>();
            for (String field : fieldsStr.split(",")) {
                fields.add(field.trim());
            }

            // 提取值
            String valuesStr = ReUtil.getGroup1(valuesPattern, statement);
            if (valuesStr == null || valuesStr.isEmpty()) {
                continue;
            }

            // 使用正则表达式匹配所有值
            List<String> values = ReUtil.findAll("'[^']*'|null|\\d+", valuesStr, 0);

            // 清理值（去除多余的引号）
            for (int i = 0; i < values.size(); i++) {
                String v = values.get(i);
                if (!"null".equals(v)) {
                    values.set(i, v.replaceAll("^'|'$", ""));
                } else {
                    values.set(i, null);
                }
            }

            // 创建字段和值的映射
            Map<String, String> fieldValueMap = new HashMap<>();
            for (int i = 0; i < Math.min(fields.size(), values.size()); i++) {
                fieldValueMap.put(fields.get(i), values.get(i));
            }

            // 提取需要的字段
            Map<String, String> result = new HashMap<>();
            for (String field : neededFields) {
                result.put(field, fieldValueMap.get(field));
            }
            allResults.add(result);
        }
        List<String> list = new ArrayList<>();
        // 统一输出结果，按制表符分隔，每行一条SQL数据
        for (Map<String, String> result : allResults) {
            StringBuilder line = new StringBuilder();
            for (String field : neededFields) {
                String value = convertSqlToSingleLine(result.get(field));
                line.append(value == null ? "" : value).append("\t");
            }
            line.append("强制更新").append("\t");
            line.append(CollUtil.join(selectTenant, ",")).append("\t");
            // 移除最后多余的制表符
            if (line.length() > 0) {
                line.setLength(line.length() - 1);
            }
            list.add(line.toString());
        }

        return list;
    }
    public void setClipboardText(String text) {
        // 获取CopyPasteManager实例
        CopyPasteManager manager = CopyPasteManager.getInstance();

        // 创建StringSelection对象
        StringSelection selection = new StringSelection(text);

        // 设置剪贴板内容
        manager.setContents(selection);
    }
    /**
     * 初始化表格编辑器和渲染器
     */
    private void initTableEditors() {
        // 第5列是角色编码(roleCode)，索引为4
        TableColumn roleCodeColumn = table.getColumnModel().getColumn(4);
        String processDefKey = nodeList.get(0).getProcessDefKey();
        String tenantId = StrUtil.subAfter(processDefKey, "_", true);
        
        // 初始化现有数据
        for (int row = 0; row < table.getRowCount(); row++) {
            String roleCode = (String) table.getValueAt(row, 4);
            String roleName = (String) table.getValueAt(row, 5);
            if (StrUtil.isNotEmpty(roleCode)) {
                // 获取角色列表但不自动设置角色名称
                populateRoleListForRow(row, roleCode, tenantId);
            }
            if (StrUtil.isNotEmpty(roleName)) {
                selectedRoleNames.put(row, roleName);
            }
        }

        // 创建角色编码编辑器 - 优化焦点处理
        DefaultCellEditor roleCodeEditor = new DefaultCellEditor(new JTextField()) {
            private int editingRow = -1;
            private String originalRoleCode = null;
            
            @Override
            public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
                editingRow = row;
                originalRoleCode = (String) value;
                JTextField textField = (JTextField) super.getTableCellEditorComponent(table, value, isSelected, row, column);
                // 确保文本框在编辑时有唯一实例
                textField.putClientProperty("JTextField.noKeyNavigation", Boolean.TRUE);
                return textField;
            }
            
            @Override
            public boolean stopCellEditing() {
                String newRoleCode = ((JTextField)getComponent()).getText();
                
                // 只有当角色编码发生变化时才更新
                if (editingRow >= 0 && !Objects.equals(newRoleCode, originalRoleCode)) {
                    if (StrUtil.isNotEmpty(newRoleCode)) {
                        populateRoleListForRow(editingRow, newRoleCode, tenantId);
                        
                        // 判断是否需要自动设置角色名称
                        Map<String, List<RoleDTO>> codeRolesMap = rowRoleCodeMap.get(editingRow);
                        if (codeRolesMap != null) {
                            List<RoleDTO> roles = codeRolesMap.get(newRoleCode);
                            if (roles != null && roles.size() == 1) {
                                // 获取当前角色名称
                                String currentRoleName = (String) table.getValueAt(editingRow, 5);
                                if (StrUtil.isEmpty(currentRoleName)) {
                                    // 如果当前角色名称为空，则自动填充
                                    String newRoleName = roles.get(0).getRoleName();
                                    table.setValueAt(newRoleName, editingRow, 5);
                                    selectedRoleNames.put(editingRow, newRoleName);
                                }
                            }
                        }
                    }
                }
                
                return super.stopCellEditing();
            }
        };
        
        // 设置角色编码列的编辑器
        roleCodeColumn.setCellEditor(roleCodeEditor);

        // 创建角色名称的编辑器 - 优化焦点处理
        DefaultCellEditor roleNameEditor = new DefaultCellEditor(new JTextField()) {
            private JComboBox<String> comboBox;
            private int editingRow = -1;
            
            @Override
            public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
                editingRow = row;
                comboBox = null;  // 重置comboBox，确保没有角色编码时不使用下拉框
                
                // 获取当前行的角色编码
                String roleCode = (String) table.getValueAt(row, 4);
                
                // 只有当角色编码不为空且有对应的角色列表时才使用下拉框
                if (StrUtil.isNotEmpty(roleCode) && rowRoleCodeMap.containsKey(row)) {
                    Map<String, List<RoleDTO>> codeRolesMap = rowRoleCodeMap.get(row);
                    List<RoleDTO> roles = codeRolesMap.get(roleCode);
                    
                    if (roles != null && !roles.isEmpty()) {
                        // 创建下拉框并填充选项
                        comboBox = new JComboBox<>();
                        // 防止下拉框焦点问题
                        comboBox.putClientProperty("JComboBox.isTableCellEditor", Boolean.TRUE);
                        
                        for (RoleDTO role : roles) {
                            comboBox.addItem(role.getRoleName());
                        }
                        
                        // 如果有已保存的选择，优先使用它
                        String savedSelection = selectedRoleNames.get(row);
                        if (savedSelection != null) {
                            boolean found = false;
                            for (int i = 0; i < comboBox.getItemCount(); i++) {
                                if (comboBox.getItemAt(i).equals(savedSelection)) {
                                    comboBox.setSelectedIndex(i);
                                    found = true;
                                    break;
                                }
                            }
                            
                            // 如果保存的选择不在列表中，添加它
                            if (!found) {
                                comboBox.addItem(savedSelection);
                                comboBox.setSelectedItem(savedSelection);
                            }
                        } else if (value != null && !StrUtil.isEmpty(value.toString())) {
                            // 否则使用当前值
                            String currentValue = value.toString();
                            boolean found = false;
                            for (int i = 0; i < comboBox.getItemCount(); i++) {
                                if (comboBox.getItemAt(i).equals(currentValue)) {
                                    comboBox.setSelectedIndex(i);
                                    found = true;
                                    break;
                                }
                            }
                            
                            // 如果当前值不在列表中，添加它
                            if (!found) {
                                comboBox.addItem(currentValue);
                                comboBox.setSelectedItem(currentValue);
                            }
                        }
                        
                        return comboBox;
                    }
                }
                
                // 如果没有角色列表或角色编码为空，使用文本框
                JTextField textField = (JTextField) super.getTableCellEditorComponent(table, value, isSelected, row, column);
                // 防止文本框焦点问题
                textField.putClientProperty("JTextField.noKeyNavigation", Boolean.TRUE);
                return textField;
            }
            
            @Override
            public boolean stopCellEditing() {
                if (editingRow >= 0) {
                    if (comboBox != null) {
                        Object selectedItem = comboBox.getSelectedItem();
                        if (selectedItem != null) {
                            // 保存选择的角色名称
                            selectedRoleNames.put(editingRow, selectedItem.toString());
                        }
                    } else {
                        // 如果使用的是文本框，保存输入的文本
                        String text = (String) super.getCellEditorValue();
                        if (StrUtil.isNotEmpty(text)) {
                            selectedRoleNames.put(editingRow, text);
                        } else {
                            // 如果文本为空，移除保存的角色名称
                            selectedRoleNames.remove(editingRow);
                        }
                    }
                }
                
                return super.stopCellEditing();
            }
            
            @Override
            public Object getCellEditorValue() {
                if (comboBox != null) {
                    return comboBox.getSelectedItem();
                } else {
                    return super.getCellEditorValue();
                }
            }
        };
        
        // 设置角色名称列的编辑器
        TableColumn roleNameColumn = table.getColumnModel().getColumn(5);
        roleNameColumn.setCellEditor(roleNameEditor);

        // 为表格添加焦点恢复监听，防止失焦后再聚焦导致的闪烁
        table.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusGained(java.awt.event.FocusEvent evt) {
                // 确保在表格获得焦点时停止任何可能正在进行的编辑
                if (table.isEditing()) {
                    table.getCellEditor().stopCellEditing();
                }
            }
        });
    }
    
    /**
     * 获取角色列表并保存到行数据映射中
     */
    private void populateRoleListForRow(int row, String roleCode, String tenantId) {
        List<RoleDTO> roles = getRoleByCode(roleCode, tenantId);
        if (roles != null && !roles.isEmpty()) {
            // 使用嵌套Map保存每行的每个角色编码对应的角色列表
            Map<String, List<RoleDTO>> codeRolesMap = rowRoleCodeMap.computeIfAbsent(row, k -> new HashMap<>());
            codeRolesMap.put(roleCode, roles);
        }
    }

    /**
     * 导出角色人员
     */
    private void expRoleFile() {
        getTableData();
        Set<String> selectTenant = getSelectTenant();
        if (CollUtil.isEmpty(selectTenant)) {
            // NotificationUtils.notifyError("请选择租户", "提示", anActionEvent.getProject());
            Messages.showMessageDialog(
                    "请选择租户",// 弹框内容
                    "提示",   // 弹框标题
                    Messages.getErrorIcon() // 使用的信息图标
            );
            return;
        }
        List<RoleExport> roleList = new ArrayList<>();
        List<RolePersonExport> rolePersonList = new ArrayList<>();
        for (Node node : nodeList) {
            List<NodeItem> taskList = node.getUserTaskList()
                    .stream()
                    .filter(el -> StrUtil.isNotBlank(el.getRoleCode()))
                    .toList();

            for (String tenantId : selectTenant) {
                for (NodeItem item : taskList) {
                    RoleExport role = new RoleExport();
                    role.setRoleCode(item.getRoleCode());
                    role.setRoleName(item.getRoleName());
                    role.setRoleType("FLOW");
                    role.setUpdateStrategy("强制更新");
                    role.setTenant(tenantId);
                    roleList.add(role);
                    for (String receive : item.getReceive().split(",")) {
                        RolePersonExport rolePerson = new RolePersonExport();
                        rolePerson.setRoleCode(item.getRoleCode());
                        rolePerson.setPersonCode(receive);
                        rolePerson.setUpdateStrategy("强制更新");
                        rolePerson.setTenantId(tenantId);
                        rolePersonList.add(rolePerson);
                    }
                }
            }

        }


        // 2. 配置多Sheet参数
        List<Map<String, Object>> sheetsList = new ArrayList<>();

// Sheet1配置
        Map<String, Object> sheet1Map = new HashMap<>();
        sheet1Map.put("title", new ExportParams("", "角色")); // 标题和Sheet名称
        sheet1Map.put("entity", RoleExport.class); // 实体类
        sheet1Map.put("data", roleList);   // 数据集合
        sheetsList.add(sheet1Map);

// Sheet2配置
        Map<String, Object> sheet2Map = new HashMap<>();
        sheet2Map.put("title", new ExportParams("", "角色-人员"));
        sheet2Map.put("entity", RolePersonExport.class);
        sheet2Map.put("data", rolePersonList);
        sheetsList.add(sheet2Map);

// 3. 导出Excel
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.XSSF); // 使用XSSF格式（.xlsx）
        Project project = anActionEvent.getProject();

        FileChooserDescriptor descriptor =
                new FileChooserDescriptor(false, true, false, false, false, false);
        final VirtualFile virtualFile = FileChooser.chooseFile(descriptor, project, null);
        String path = virtualFile.getPath();
        if (StrUtil.isBlank(path)) {
            return;
        }
        try {
            workbook.write(new FileOutputStream(path + StrUtil.format("/角色人员配置.xlsx")));
            FileNavigator.showFileNotification(project, path + StrUtil.format("/角色人员配置.xlsx"),
                    "文件导出成功", "", "打开文件");

        } catch (IOException e) {
            NotificationUtils.notifyError("导出失败", "提示", project);
            throw new RuntimeException(e);
        }
    }

    private Set<String> getSelectTenant() {
        Set<String> tenantSet = Arrays.stream(tenantPanel.getComponents()).filter(
                        component -> component instanceof JCheckBox)
                .filter(component -> ((JCheckBox) component).isSelected())
                .map(Component::getName)
                .filter(el->!"inter".equals(el))
                .collect(Collectors.toSet());
        return tenantSet;
    }

    public void getApplicationList(JPanel jPanel) {
        String result = HttpRequest.get("http://*************/sys-web-jwt/application/list?appName=&appType=&busiType=&pageNum=1&pageSize=9999&orderByColumns=T1.sort,T1.create_time")
                .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIya3FTbmE1TmoweTBmQzhETEJPd1dVdmxuRWNyZUQ4NiJ9.0iPPXwIiMHdQ0UVJ_bL5SYC4y_FLu6pGaJzmu0WV1Ao")
                .execute()
                .body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        List<ApplicationDto> application = jsonObject.getBeanList("records", ApplicationDto.class);
        for (ApplicationDto dto : application) {
            JCheckBox checkBox = new JCheckBox(dto.getAppName());
            checkBox.setName(dto.getAppId());
            jPanel.add(checkBox);
        }
    }

    public List<RoleDTO> getRoleByCode(String roleCode,String tenantId){
        FlowEnvironment env = getCurrentEnvironment();
        if (env == null) {
            return new ArrayList<>();
        }
        
        String url = env.getUrl();
        if (StrUtil.isBlank(roleCode)) {
            return new ArrayList<>();
        }
        
        // 确保URL不以斜杠结尾
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        
        try {
            String result = HttpRequest.get(StrUtil.format("https://www.rzdata.net/sys-web-jwt/role/{}/role?roleId={}&roleName=&roleType=&pageNum=1&pageSize=10",tenantId,roleCode))
                    .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ6UThLa0k5SEl0bkx0V2FXWW1mbjlLeTJ3OWxIZVhlMiJ9.LeOfenI2PMtXVL1W2sdwuw-YggAvXzV3Ne6RBwTef9k" )
                    .header("tenantid",tenantId)
                    .execute()
                    .body();
                    
            JSONObject jsonObject = JSONUtil.parseObj(result);
            return jsonObject.getJSONObject("result").getBeanList("records", RoleDTO.class);
        } catch (Exception e) {
            NotificationUtils.notifyError("获取角色信息失败: " + e.getMessage(), "错误", anActionEvent.getProject());
            return new ArrayList<>();
        }
    }

    private static Credential INSTANCE = new Credential("AKID4IXccMhHdjcJm0wqPjndHhspkbDbGeJz", "aw3v5B3h8OPQO7j8JPGsFNHwMXTAcprf");

    /**
     * 腾讯翻译
     *
     * @param query 需要翻译的内容 {@link List}<{@link String}>
     * @param from  源语言
     * @param to    目标语言
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> translate(List<String> query, String from, String to) {
        Map<String, String> infoMap = new HashMap<>();
        try {
            TextTranslateBatchRequest req = new TextTranslateBatchRequest();
            req.setSourceTextList(query.toArray(new String[0]));
            req.setSource(from);
            req.setTarget(to);
            req.setProjectId(0L);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("tmt.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            TmtClient client = new TmtClient(INSTANCE, "ap-beijing", clientProfile);
            TextTranslateBatchResponse resp = client.TextTranslateBatch(req);
            String[] targetTextList = resp.getTargetTextList();
            for (int i = 0; i < targetTextList.length; i++) {
                String value = targetTextList[i].replace("[", "【").replace("]", "】");
                if ("en".equals(to)) {
                    value = StrUtil.upperFirst(value);
                }
                infoMap.put(query.get(i), value);
            }
        } catch (TencentCloudSDKException e) {
            // throw new RuntimeException(e);
            return infoMap;
        }
        return infoMap;
    }

    public void export(List<Node> nodes) {
        NodeSelectionDialog selectionDialog = new NodeSelectionDialog(nodes);
        if (selectionDialog.showAndGet()) {
            Map<Node, List<NodeItem>> selectedNodes = selectionDialog.getSelectedNodes();
            
            if (selectedNodes.isEmpty()) {
                NotificationUtils.notifyWarning("没有选择任何节点", "提示", anActionEvent.getProject());
                return;
            }
            
            ExportParams exportParams = new ExportParams("国际化导入导出模板", "国际化");
            exportParams.setDictHandler(new ExcelDicHandler());
            exportParams.setCreateHeadRows(true);
            List<InternationalExport> list = new ArrayList<>();
            Set<String> selectTenant = getSelectTenant();
            if (StrUtil.isNotBlank(tenantField.getText())) {
                selectTenant.addAll(Arrays.asList(tenantField.getText().split(",")));
            } else {
                String processDefKey = nodes.get(0).getProcessDefKey();
                String tenant = StrUtil.subAfter(processDefKey, "_", true);
                selectTenant.add(tenant);
            }
            Map<String, String> translate = new HashMap<>();
            for (String tenant : selectTenant) {
                for (Map.Entry<Node, List<NodeItem>> entry : selectedNodes.entrySet()) {
                    Node node = entry.getKey();
                    List<NodeItem> selectedItems = entry.getValue();
                    List<String> nodeNameList = new CopyOnWriteArrayList<>(selectedItems.stream()
                            .map(NodeItem::getActivityDefName)
                            .toList());
                    Iterator<String> iterator = nodeNameList.iterator();

                    while (iterator.hasNext()) {
                        String name = iterator.next();
                        if (translate.containsKey(name)) {
                            nodeNameList.remove(name);
                        }
                    }


                    if (CollUtil.isNotEmpty(nodeNameList)) {
                        translate.putAll(translate(nodeNameList, "zh", "en"));
                    }
                    String processDefKey = node.getProcessDefKey();
                    processDefKey = StrUtil.subBefore(node.getProcessDefKey(), "_", true);
                    
                    for (NodeItem item : selectedItems) {
                        InternationalExport internationalExport = new InternationalExport();
                        internationalExport.setCode("flow." + processDefKey + "_" + tenant + "_" + item.getActivityDefId());
                        internationalExport.setName(item.getActivityDefName());
                        internationalExport.setLang("zh");
                        internationalExport.setUpdateStrategy("强制更新");
                        internationalExport.setType("front");
                        internationalExport.setTenantId(tenant);
                        list.add(internationalExport);

                        InternationalExport internationalExportEn = new InternationalExport();
                        internationalExportEn.setCode("flow." + processDefKey + "_" + tenant + "_" + item.getActivityDefId());
                        internationalExportEn.setName(translate.get(item.getActivityDefName()));
                        internationalExportEn.setLang("en");
                        internationalExportEn.setUpdateStrategy("强制更新");
                        internationalExportEn.setType("front");
                        internationalExportEn.setTenantId(tenant);
                        list.add(internationalExportEn);
                    }
                }
            }

            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, InternationalExport.class, list);
            Project project = anActionEvent.getProject();

            FileChooserDescriptor descriptor =
                    new FileChooserDescriptor(false, true, false, false, false, false);
            final VirtualFile virtualFile = FileChooser.chooseFile(descriptor, project, null);
            String path = virtualFile.getPath();
            if (StrUtil.isBlank(path)) {
                return;
            }
            try {
                workbook.write(new FileOutputStream(path + StrUtil.format("/流程国际化导入导出模板.xlsx")));
                FileNavigator.showFileNotification(project, path + StrUtil.format("/流程国际化导入导出模板.xlsx"),
                        "国际化文件导出成功", "", "打开文件");

            } catch (IOException e) {
                NotificationUtils.notifyError("导出失败", "提示", project);
                throw new RuntimeException(e);
            }
        }
    }

    public Map<String, String> getFlowTenantMap() {
        FlowEnvironment env = getCurrentEnvironment();
        if (env == null) return new HashMap<>();

        String url = env.getUrl();
        // 确保URL不以斜杠结尾
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }

        // 确保URL路径正确
        if (!url.endsWith("/ebpm-process-manage")) {
            // 如果URL不包含ebpm-process-manage，则添加
            if (!url.contains("/ebpm-process-manage")) {
                url += "/ebpm-process-manage";
            }
        }

        url += "/applicationMgr/list.do";

        try {
            // 设置cookie
            String cookie = getFlowCookie(); // 完整的cookie字符串
            // 设置请求体
            String body = "appName=&pageNumber=1&pageSize=100&sortColumns=";
            // 发送请求
            String result = HttpUtil.createPost(url)
                    .cookie(cookie)
                    .body(body)
                    .execute()
                    .body();

            // 先检查返回的内容是否是JSON格式
            if (result.trim().startsWith("{") && result.trim().endsWith("}")) {
                // 如果是JSON格式，尝试从JSON中提取数据
                try {
                    JSONObject jsonObject = JSONUtil.parseObj(result);
                    if (jsonObject.containsKey("records")) {
                        List<Map> records = jsonObject.getBeanList("records", Map.class);
                        Map<String, String> tenantMap = new HashMap<>();
                        for (Map record : records) {
                            String appId = String.valueOf(record.get("appId"));
                            String appName = String.valueOf(record.get("appName"));
                            tenantMap.put(appId.replace("CRM_", ""), appName);
                        }
                        return tenantMap;
                    }
                } catch (Exception e) {
                    NotificationUtils.notifyWarning("解析JSON数据失败: " + e.getMessage(), "提示", anActionEvent.getProject());
                }
            }

            // 如果不是JSON或者JSON解析失败，尝试解析HTML
            try {
                Document doc = Jsoup.parse(result);
                Elements rows = doc.select("table#sample-table-1 tbody tr");

                Map<String, String> tenantMap = new HashMap<>();

                for (org.jsoup.nodes.Element row : rows) {
                    Elements tds = row.select("td");
                    if (tds.size() >= 3) {
                        String appId = tds.get(1).text().trim();
                        String appName = tds.get(2).text().trim();
                        tenantMap.put(appId.replace("CRM_", ""), appName);
                    }
                }
                return tenantMap;
            } catch (Exception e) {
                NotificationUtils.notifyWarning("解析HTML失败: " + e.getMessage(), "提示", anActionEvent.getProject());
            }
        } catch (Exception e) {
            NotificationUtils.notifyError("获取租户信息失败: " + e.getMessage(), "错误", anActionEvent.getProject());
        }

        // 如果都失败了，返回空map
        return new HashMap<>();
    }

    public String getFlowCookie() {
        FlowEnvironment env = getCurrentEnvironment();
        if (env == null) return "";

        String url = env.getUrl();
        // 确保URL不以斜杠结尾
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }

        // 确保URL路径正确
        if (!url.endsWith("/ebpm-process-manage")) {
            // 如果URL不包含ebpm-process-manage，则添加
            if (!url.contains("/ebpm-process-manage")) {
                url += "/ebpm-process-manage";
            }
        }

        url += "/pages/user/login.do";

        try {
            // 设置请求头
            HttpRequest request = HttpRequest.post(url)
                    .cookie(getGateWayCookie())
                    .body("userId=" + env.getUsername() + "&password=" + env.getPassword());
            // 发送请求
            HttpResponse response = request.execute();


            // 检查状态码
            if (response.getStatus() != 200) {
                NotificationUtils.notifyWarning("登录失败，状态码: " + response.getStatus(), "提示", anActionEvent.getProject());
                return "";
            }

            // 提取Cookies
            List<HttpCookie> cookies = response.getCookies();
            if (cookies.isEmpty()) {
                NotificationUtils.notifyWarning("没有获取到Cookie，可能登录失败", "提示", anActionEvent.getProject());
                return "";
            }

            StringJoiner joiner = new StringJoiner("; ");
            for (HttpCookie cookie : cookies) {
                joiner.add(StrUtil.format("{}={}", cookie.getName(), cookie.getValue()));
            }
            return joiner.toString();
        } catch (Exception e) {
            NotificationUtils.notifyError("获取Cookie失败: " + e.getMessage(), "错误", anActionEvent.getProject());
            return "";
        }
    }
    public String getGateWayCookie() {
        FlowEnvironment env = getCurrentEnvironment();
        if (env == null|| ObjectUtil.isEmpty(env.getGateWayAccount())||ObjectUtil.isEmpty(env.getGateWayPwd())) return "";

        try {
            // 设置请求头
            HttpRequest request = HttpRequest.post("https://crm.vazyme.com/sso-service/login");

            // Add request body
            request.body(StrUtil.format("username={}&password={}",env.getGateWayAccount(),env.getGateWayPwd()));
            // 发送请求
            HttpResponse response = request.execute();

            // 检查状态码
            if (response.getStatus() != 200) {
                NotificationUtils.notifyWarning("登录失败，状态码: " + response.getStatus(), "提示", anActionEvent.getProject());
                return "";
            }

            // 提取Cookies
            List<HttpCookie> cookies = response.getCookies();
            if (cookies.isEmpty()) {
                NotificationUtils.notifyWarning("没有获取到Cookie，可能登录失败", "提示", anActionEvent.getProject());
                return "";
            }

            StringJoiner joiner = new StringJoiner("; ");
            for (HttpCookie cookie : cookies) {
                joiner.add(StrUtil.format("{}={}", cookie.getName(), cookie.getValue()));
            }
            return joiner.toString();
        } catch (Exception e) {
            NotificationUtils.notifyError("获取Cookie失败: " + e.getMessage(), "错误", anActionEvent.getProject());
            return "";
        }
    }


    /**
     * 上传流程文件
     */
    public void uploadFlowFile() throws IOException {
        // 获取环境和认证信息
        FlowEnvironment env = getCurrentEnvironment();
        if (env == null) {
            NotificationUtils.notifyError("请先配置环境", "上传失败", anActionEvent.getProject());
            return;
        }

        // 准备租户文件数据
        Map<String, Map<String, byte[]>> tenantMap = prepareTenantFiles();
        if (tenantMap.isEmpty()) {
            NotificationUtils.notifyWarning("没有可上传的文件", "提示", anActionEvent.getProject());
            return;
        }

        // 获取认证信息和URL
        String flowCookie = getFlowCookie();
        String url = buildUploadUrl(env);

        // 执行上传过程
        uploadFilesForTenants(tenantMap, url, flowCookie);

        NotificationUtils.notifySuccess("上传并发布完成", "操作完成", anActionEvent.getProject());
    }

    /**
     * 准备各租户的文件数据
     */
    private Map<String, Map<String, byte[]>> prepareTenantFiles() {
        Map<String, Map<String, byte[]>> tenantMap = new HashMap<>();

        for (File file : fileList) {
            Map<String, FastByteArrayOutputStream> zipData = unzipData(IoUtil.toStream(file));
            processFileEntries(zipData, tenantMap);
        }

        return tenantMap;
    }

    /**
     * 处理解压后的文件条目
     */
    private void processFileEntries(Map<String, FastByteArrayOutputStream> zipData, Map<String, Map<String, byte[]>> tenantMap) {
        Set<String> selectTenant = getSelectTenant();
        if (CollUtil.isEmpty(selectTenant)) {
            return;
        }

        for (Map.Entry<String, FastByteArrayOutputStream> entry : zipData.entrySet()) {
            String xmlVal = entry.getValue().toString();
            String currentTenant = entry.getKey().substring(0, entry.getKey().indexOf("."));
            String suffix = entry.getKey().substring(entry.getKey().indexOf("."));
            String proDefKey = StrUtil.subBefore(currentTenant, "_", true);

            // 处理每个选定的租户
            for (String tenantId : selectTenant) {
                String newXmlVal = xmlVal;
                String fileName = proDefKey + "_" + tenantId + suffix;

                if (suffix.contains("bpmn20")) {
                    newXmlVal = xmlVal.replaceAll(currentTenant, proDefKey + "_" + tenantId);
                }

                // 将内容存储到内存中
                tenantMap.computeIfAbsent(tenantId, k -> new HashMap<>())
                        .put(fileName, newXmlVal.getBytes(StandardCharsets.UTF_8));
            }
        }
    }

    /**
     * 构建上传URL
     */
    private String buildUploadUrl(FlowEnvironment env) {
        String url = env.getUrl();

        // 确保URL格式正确
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }

        if (!url.endsWith("/ebpm-process-manage")) {
            if (!url.contains("/ebpm-process-manage")) {
                url += "/ebpm-process-manage";
            }
        }

        return url + "/procDefMgr/importProcessAllFile.do";
    }

    /**
     * 为每个租户上传文件
     */
    private void uploadFilesForTenants(Map<String, Map<String, byte[]>> tenantMap, String url, String flowCookie) {
        for (Map.Entry<String, Map<String, byte[]>> entry : tenantMap.entrySet()) {
            String tenantId = entry.getKey();
            Map<String, byte[]> files = entry.getValue();

            if (files.isEmpty()) {
                continue;
            }

            // 切换租户
            changeTenantId(tenantId);

            // 创建并上传ZIP文件
            try {
                byte[] zipData = createZipFile(files);
                uploadZipFile(url, flowCookie, tenantId, zipData);
            } catch (Exception e) {
                NotificationUtils.notifyError("为租户 " + tenantId + " 处理文件时出错: " + e.getMessage(),
                        "上传失败", anActionEvent.getProject());
            }
        }
    }

    /**
     * 在内存中创建ZIP文件
     */
    private byte[] createZipFile(Map<String, byte[]> files) throws IOException {
        FastByteArrayOutputStream zipOutputStream = new FastByteArrayOutputStream();

        try (ZipOutputStream zipOut = new ZipOutputStream(zipOutputStream)) {
            // 逐个添加文件到ZIP
            for (Map.Entry<String, byte[]> fileEntry : files.entrySet()) {
                ZipEntry zipEntry = new ZipEntry(fileEntry.getKey());
                zipOut.putNextEntry(zipEntry);
                zipOut.write(fileEntry.getValue());
                zipOut.closeEntry();
            }
        }

        return zipOutputStream.toByteArray();
    }

    /**
     * 上传ZIP文件
     */
    private void uploadZipFile(String url, String flowCookie, String tenantId, byte[] zipData) {
        String zipFileName = "process_" + tenantId + ".zip";

        // 使用BytesResource类避免数组越界异常
        HttpResponse response = HttpRequest.post(url)
                .cookie(flowCookie)
                .form("file", new BytesResource(zipData, zipFileName))
                .execute();

        if (response.getStatus() == 200) {
            // 发布流程
            pushFlow(flowCookie);
        } else {
            NotificationUtils.notifyError("租户 " + tenantId + " 上传失败：" + response.body(),
                    "上传失败", anActionEvent.getProject());
        }
    }
    public void pushFlow(String flowCookie) {

        FlowEnvironment env = getCurrentEnvironment();
        if (env == null) {
            return;
        }

        String url = env.getUrl();
        // 确保URL不以斜杠结尾
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }

        // 确保URL路径正确
        if (!url.endsWith("/ebpm-process-manage")) {
            // 如果URL不包含ebpm-process-manage，则添加
            if (!url.contains("/ebpm-process-manage")) {
                url += "/ebpm-process-manage";
            }
        }

        url += "/procDefMgr/list.do";

        String html = HttpUtil.createPost(url)
                .cookie(flowCookie)
                .body("processMgrState=UNRELEASE")
                .execute()
                .body();

        Document doc = Jsoup.parse(html);
        Elements rows = doc.select("#sample-table-1 tbody tr");
        List<String> pushUrlList = new ArrayList<>();
        for (org.jsoup.nodes.Element row : rows) {
            Elements tds = row.select("td");
            Elements a = tds.get(8).select(" div > ul > li:nth-child(1) > a");
            String onclick = a.attr("onclick");
            String pushUrl = StrUtil.subBetween(onclick, "'", "'");
            if(!pushUrl.startsWith("http")){
                pushUrl=pushUrl.replace("/ebpm-process-manage", "");
            }

            pushUrlList.add(pushUrl);
        }
        for (String suffix : pushUrlList) {
            if(!suffix.startsWith("http")){
                suffix=env.getUrl() + suffix;
            }
            HttpUtil.createPost(suffix)
                    .cookie(flowCookie)
                    .execute();
        }


    }


    public void changeTenantId(String tenantId) {
        FlowEnvironment env = getCurrentEnvironment();
        if (env == null) {
            return;
        }

        String url = env.getUrl();
        // 确保URL不以斜杠结尾
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }

        // 确保URL路径正确
        if (!url.endsWith("/ebpm-process-manage")) {
            // 如果URL不包含ebpm-process-manage，则添加
            if (!url.contains("/ebpm-process-manage")) {
                url += "/ebpm-process-manage";
            }
        }

        url += "/pages/user/changeApp.do";
        String flowCookie = getFlowCookie();
        HttpRequest.post(url)
                .cookie(flowCookie)
                .body("appId=CRM_" + tenantId)
                .execute()
                .body();
    }

    


    public String originalName(String fileName, Map<String, String> flowTenantMap) {
        Optional<String> any = flowTenantMap.values()
                .stream()
                .filter(fileName::contains)
                .findAny();

        if (any.isPresent()) {
            String s = any.get();
            return fileName.replace(s, "");
        }
        return fileName;
    }
    @NotNull
    private DefaultTableModel getModel() {
        if (ObjectUtil.isNull(table)) {
            return new DefaultTableModel(data, columnNames) {
                boolean[] canEdit = {false, false, true, true, true, true, true, true};
                @Override
                public boolean isCellEditable(int rowIndex, int columnIndex) {
                    return canEdit[columnIndex];
                }
            };
        }
        DefaultTableModel model = (DefaultTableModel) table.getModel();
        model.setRowCount(0);

        for (Object[] rowData : data) {
            model.addRow(rowData);
        }
        return model;
    }

    /**
     * 设置读取数据按钮是否禁用
     *
     * @param button
     */
    public void setSection(JButton button) {
        PluginSettings instance = PluginSettings.getInstance();
        PluginSettings.State state = instance.getState();
        if (StrUtil.isEmpty(state.pluginWorkFlowData)) {
            button.setEnabled(false);
        }
    }


    @Override
    protected void doOKAction() {
        Project project = anActionEvent.getProject();
        getTableData();
        VelocityEngine velocityEngine = new VelocityEngine();
        String templateContent = getTemplateContent();
        VelocityContext context = new VelocityContext();
        String className = classField.getText();
        String finalClass = classComboBox.getEditor().getItem().toString();
        boolean flag = StrUtil.isNotBlank(finalClass);
        for (Node node : nodeList) {
            StringWriter sw = new StringWriter();
            context.put("className", StrUtil.upperFirst(StrUtil.toCamelCase(node.getProcessDefKey())) + "WorkFlow");
            context.put("processDefKey", node.getProcessDefKey());
            context.put("processDefKeyFinal", StringUtils.upperCase(node.getProcessDefKey()));
            context.put("nodeList", node.getUserTaskList());
            context.put("package", ClassUtils.getPackage(anActionEvent));
            context.put("finalConstant", !flag);
            context.put("description", node.getProcessDefName());
            context.put("finalConstantClass", finalClass);
            context.put("createTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            velocityEngine.evaluate(context, sw, "Work Flow Generate", templateContent);
            WriteCommandAction.runWriteCommandAction(project, () -> {
                PsiFile file = PsiFileFactory.getInstance(project).createFileFromText(StrUtil.upperFirst(StrUtil.toCamelCase(node.getProcessDefKey())) + "WorkFlow" + ".java", JavaFileType.INSTANCE, sw.toString());
                // 格式化代码
                CodeStyleManager codeStyleManager = CodeStyleManager.getInstance(project);
                PsiElement reformat = codeStyleManager.reformat(file);
                ClassUtils.createClass(context.get("className").toString(), reformat.getText(), anActionEvent);
                // 如果选择了常量类则去常量类生成常量
                if (flag) {
                    createConstantField(project, finalClass, node);
                }
            });
        }
        // 打开文件
        EditorUtils.toEditorFile(className, anActionEvent);
        //生成代码之后会把暂存的数据删掉
        clearSectionData();
        super.doOKAction();
    }

    /**
     * 清空暂存数据
     */
    public void clearSectionData() {
        PluginSettings instance = PluginSettings.getInstance();
        PluginSettings.State state = instance.getState();
        state.pluginWorkFlowData = "";
        instance.loadState(state);
    }

    /**
     * 获取表格数据
     */
    private void getTableData() {
        if (table.isEditing()) {
            table.getCellEditor().stopCellEditing();
        }
        TableModel model = table.getModel();
        int rowCount = model.getRowCount();
        int columnCount = model.getColumnCount() - 1;
        for (int row = 0; row < rowCount; row++) {
            NodeItem nodeItem = nodeIdNameMap.get(model.getValueAt(row, 1));
            Object nextDefName = model.getValueAt(row, columnCount);
            NodeItem nextNodeItem = nodeItemMap.get(nextDefName);
            if (ObjectUtil.isNull(nextNodeItem)) {
                NotificationUtils.notifyError(StrUtil.format("下一节点【{}】不存在", nextDefName), "提示", anActionEvent.getProject());
            }
            Assert.notNull(nextNodeItem, "下一节点不存在");
            nodeItem.setNextActivityDefId(nextNodeItem.getActivityDefId());
            nodeItem.setType(type.get(model.getValueAt(row, 2)));
            nodeItem.setDeptCode((String) model.getValueAt(row, 3));
            nodeItem.setRoleCode((String) model.getValueAt(row, 4));
            nodeItem.setRoleName((String) model.getValueAt(row, 5));
            nodeItem.setReceive((String) model.getValueAt(row, 6));
            nodeItem.setNextActivityDefIdFinal(StringUtils.upperCase(nodeItem.getNextActivityDefId()));
            nodeItem.setDeptCodeUpper(StringUtils.upperCase(nodeItem.getDeptCode()));
            nodeItem.setRoleCodeUpper(StringUtils.upperCase(nodeItem.getRoleCode()));
            nodeItem.setReceiveUpper(StringUtils.upperCase(nodeItem.getReceive()));
        }
    }

    public void createConstantField(Project project, String finalClass, Node node) {
        PsiClass psiClass = ClassUtils.searchClasse(finalClass, project);
        if (ObjectUtil.isNull(psiClass)) {
            NotificationUtils.notifyError("常量类不存在", "提示", anActionEvent.getProject());
            return;
        }
        PsiElementFactory elementFactory = JavaPsiFacade.getElementFactory(project);

        List<String> fieldList = Arrays.stream(psiClass.getFields())
                .map(PsiField::getName)
                .collect(Collectors.toList());

        ArrayList<PsiField> list = new ArrayList<>();
        ArrayList<PsiField> codeList = new ArrayList<>();

        for (NodeItem nodeItem : node.getUserTaskList()) {
            // 流程环节
            if (!fieldList.contains(nodeItem.getActivityDefIdFinal())) {
                PsiField field = elementFactory.createFieldFromText(StrUtil.format(SysConstant.FIELD_TEMPLATE, nodeItem.getActivityDefIdFinal(), nodeItem.getActivityDefId()), null);
                DocumentUtils.createDocumentAppendBefore(field, nodeItem.getActivityDefName());
                list.add(field);
            }
            if (ObjectUtil.isNull(nodeItem.getType())) {
                continue;
            }
            if ((nodeItem.getType().equals(SysConstant.ROLE) || nodeItem.getType().equals(SysConstant.DEPT)) && !nodeItem.getNextActivityDefId().equals("end")) {
                String roleCode = "ROLE_" + StringUtils.upperCase(nodeItem.getRoleCode());
                if (fieldList.contains(roleCode)) {
                    continue;
                }
                PsiField field = elementFactory.createFieldFromText(StrUtil.format(SysConstant.FIELD_Type_ROLE, roleCode, nodeItem.getRoleCode()), null);
                DocumentUtils.createDocumentAppendBefore(field, "角色编码:" + nodeItem.getRoleCode());
                codeList.add(field);

            }
            if (nodeItem.getType().equals(SysConstant.DEPT)) {
                String deptCode = "DEPT_" + StringUtils.upperCase(nodeItem.getDeptCode());
                if (fieldList.contains(deptCode)) {
                    continue;
                }
                PsiField field = elementFactory.createFieldFromText(StrUtil.format(SysConstant.FIELD_Type_DEPT, deptCode, nodeItem.getDeptCode()), null);
                DocumentUtils.createDocumentAppendBefore(field, "部门编码:" + nodeItem.getDeptCode());
                codeList.add(field);

            }
            if (nodeItem.getType().equals(SysConstant.USER)) {
                String receive = "USER_" + StringUtils.upperCase(nodeItem.getReceive());
                if (fieldList.contains(receive)) {
                    continue;
                }
                PsiField field = elementFactory.createFieldFromText(StrUtil.format(SysConstant.FIELD_Type_USER, receive, nodeItem.getReceive()), null);
                DocumentUtils.createDocumentAppendBefore(field, "用户标识:" + nodeItem.getReceive());
                codeList.add(field);

            }
        }
        String processDefKey = StringUtils.upperCase(node.getProcessDefKey());

        if (!fieldList.contains(processDefKey)) {
            // 流程定义key
            PsiField field = elementFactory.createFieldFromText(StrUtil.format(SysConstant.FIELD_TEMPLATE_PROCESS_DEF_KEY, processDefKey, node.getProcessDefName()), null);
            DocumentUtils.createDocumentAppendBefore(field, "流程定义 KEY:" + node.getProcessDefName());
            codeList.add(field);
        }
        list.addAll(codeList);
        for (PsiField psiField : list) {
            psiClass.add(psiField);
        }
    }


    @NotNull
    public String getTemplateContent() {
        URL resource = getClass().getResource("/templates/crm_flow.java.vm");
        String templateContent = null;
        try {
            templateContent = StringUtil.convertLineSeparators(UrlUtil.loadText(resource));
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
        return templateContent;
    }


    /**
     * 获取zip数据
     *
     * @param stream
     * @return
     */
    public static Map<String, FastByteArrayOutputStream> zipData(InputStream stream) {
        ZipInputStream zipInputStream = new ZipInputStream(stream);
        Map<String, FastByteArrayOutputStream> data = new HashMap<>();
        ZipEntry entry;
        try {
            while (true) {
                if ((entry = zipInputStream.getNextEntry()) == null) {
                    break;
                }
                String name = entry.getName();
                if (!name.contains("bpmn20")) {
                    continue;
                }
                data.put(name.substring(0, name.indexOf(".")), IoUtil.read(zipInputStream, false));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return data;
    }

    public static Map<String, FastByteArrayOutputStream> unzipData(InputStream stream) {
        ZipInputStream zipInputStream = new ZipInputStream(stream);
        Map<String, FastByteArrayOutputStream> data = new HashMap<>();
        ZipEntry entry;
        try {
            while (true) {
                if ((entry = zipInputStream.getNextEntry()) == null) {
                    break;
                }
                data.put(entry.getName(), IoUtil.read(zipInputStream, false));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return data;
    }

    /**
     * 更新流程名称标签
     * @param processName 流程名称
     */
    private void updateProcessNameLabel(String processName) {
        processNameLabel.setText("当前流程：" + processName);
    }

    /**
     * 加载环境配置
     */
    private void loadEnvironments() {
        PluginSettings instance = PluginSettings.getInstance();
        String flowEnvironments = instance.getState().flowEnvironments;
        String currentEnvironment = instance.getState().currentFlowEnvironment;

        envComboBox.removeAllItems();

        if (StrUtil.isNotEmpty(flowEnvironments)) {
            List<FlowEnvironment> environments = JSONUtil.toList(flowEnvironments, FlowEnvironment.class);
            for (FlowEnvironment env : environments) {
                envComboBox.addItem(env);
                if (env.getName().equals(currentEnvironment)) {
                    envComboBox.setSelectedItem(env);
                }
            }
        }
    }

    /**
     * 保存当前选择的环境
     */
    private void saveCurrentEnvironment() {
        FlowEnvironment selectedEnv = (FlowEnvironment) envComboBox.getSelectedItem();
        if (selectedEnv != null) {
            PluginSettings instance = PluginSettings.getInstance();
            PluginSettings.State state = instance.getState();
            state.currentFlowEnvironment = selectedEnv.getName();
            instance.loadState(state);
        }
    }

    /**
     * 获取当前选择的环境配置
     */
    private FlowEnvironment getCurrentEnvironment() {
        return (FlowEnvironment) envComboBox.getSelectedItem();
    }
}



