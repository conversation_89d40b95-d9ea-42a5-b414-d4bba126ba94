package club.bigtian.dialogs;

import club.bigtian.functions.SimpleFunction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;

public class WaringDialog extends DialogWrapper {
    /**
     * Creates modal {@code DialogWrapper} that can be a parent for other windows.
     * The currently active window will be the dialog's parent.
     *
     * @param project parent window for the dialog will be calculated based on focused window for the
     * specified {@code project}. This parameter can be {@code null}. In this case parent window
     * will be suggested based on current focused window.
     * @throws IllegalStateException if the dialog is invoked not on the event dispatch thread
     * @see DialogWrapper#DialogWrapper(Project, boolean)
     */
    private String fileName;
    SimpleFunction simpleFunction;

    public WaringDialog(@Nullable Project project, String fileName, SimpleFunction simpleFunction) {
        super(project);
        setTitle("警告");
        this.fileName = fileName;
        this.simpleFunction = simpleFunction;
        init();
        setSize(300, 200);
    }


    @Override
    protected @Nullable JComponent createCenterPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        JLabel label = new JLabel("是否删除该文件？" + fileName);
        panel.add(label);
        return panel;
    }

    @Override
    protected void doOKAction() {
        simpleFunction.apply();
        super.doOKAction();
    }

}
