package club.bigtian.dialogs;

import club.bigtian.constant.SysConstant;
import club.bigtian.dto.ColumnDTO;
import club.bigtian.dto.TableDTO;
import club.bigtian.persistent.PluginSettings;
import club.bigtian.study.DDLParser;
import club.bigtian.study.DDLRichTextEditor;
import club.bigtian.util.DocumentUtils;
import club.bigtian.util.NotificationUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.intellij.ide.highlighter.JavaFileType;
import com.intellij.openapi.actionSystem.*;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.*;
import com.intellij.psi.codeStyle.CodeStyleManager;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.util.IncorrectOperationException;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class DDLDialog extends DialogWrapper {
    private final DDLRichTextEditor editor;
    AnActionEvent actionEvent;
    JLabel label = new JLabel("基础配置：");

    JCheckBox dataBox = new JCheckBox(SysConstant.ANNO_LOMBOK_DATA);
    JCheckBox mybatisPlusBox = new JCheckBox("MybatisPlus");
    JCheckBox swagger = new JCheckBox("Swagger");
    JLabel tablePre = new JLabel("移除表前缀：");

    JTextField field = new JTextField();
    JButton button = new JButton("保存当前配置");


    public DDLDialog(AnActionEvent actionEvent) {
        super(true);
        this.actionEvent = actionEvent;
        setTitle("DDL转实体类");
        editor = new DDLRichTextEditor(actionEvent.getProject());
        init();
        setSize(800, 600);
        loadSettings();
    }

    public void loadSettings() {
        PluginSettings pluginSettings = PluginSettings.getInstance();
        String settingJson = pluginSettings.getState().pluginSettingJson;
        if (StrUtil.isBlank(settingJson)) {
            return;
        }
        for (Map.Entry<String, Object> entry : JSONUtil.parseObj(settingJson)) {
            if (SysConstant.CHECKBOX_LOMBOK.equalsIgnoreCase(entry.getKey())) {
                dataBox.setSelected((Boolean) entry.getValue());
            } else if (SysConstant.CHECKBOX_MYBATIS_PLUS.equalsIgnoreCase(entry.getKey())) {
                mybatisPlusBox.setSelected((Boolean) entry.getValue());

            } else if (SysConstant.CHECKBOX_SWAGGER.equalsIgnoreCase(entry.getKey())) {
                swagger.setSelected((Boolean) entry.getValue());
            } else if (SysConstant.FIELD_TABLE_PRE.equalsIgnoreCase(entry.getKey())) {
                field.setText((String) entry.getValue());
            }
        }
    }

    @Nullable
    @Override
    protected JComponent createCenterPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        Editor editorEditor = editor.getEditor();
        JPanel checkBoxPanel = new JPanel();
        new Thread(() -> {
            try {
                TimeUnit.MILLISECONDS.sleep(300);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 将编辑器内容组件设置为焦点组件
            SwingUtilities.invokeLater(() -> {

                // editor.getComponent().setVisible(true);
                editorEditor.getContentComponent().requestFocusInWindow();
            });
        }).start();
        checkBoxPanel.setLayout(new FlowLayout(FlowLayout.LEFT));

        annoCheckBox(checkBoxPanel);
        checkBoxPanel.setPreferredSize(null); // 让面板根据内容自适应大小
        // 设置 checkBoxPanel 的最大高度
        int desiredHeight = 40; // 设置您期望的高度值
        checkBoxPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, desiredHeight));

        panel.add(checkBoxPanel);

        JBScrollPane scrollPane = new JBScrollPane(editorEditor.getComponent());
        panel.add(scrollPane);
        return panel;
    }

    private void annoCheckBox(JPanel checkBoxPanel) {
        Project project = actionEvent.getProject();
        checkBoxPanel.add(label);
        checkBoxPanel.add(dataBox);
        checkBoxPanel.add(mybatisPlusBox);
        checkBoxPanel.add(swagger);
        checkBoxPanel.add(tablePre);
        field.setColumns(10);
        checkBoxPanel.add(field);
        checkBoxPanel.add(button);
        button.addActionListener(e -> {
            PluginSettings pluginSettings = PluginSettings.getInstance();
            PluginSettings.State state = pluginSettings.getState();
            JSONObject settings = JSONUtil.createObj();
            settings.set(SysConstant.CHECKBOX_LOMBOK, dataBox.isSelected());
            settings.set(SysConstant.CHECKBOX_MYBATIS_PLUS, mybatisPlusBox.isSelected());
            settings.set(SysConstant.CHECKBOX_SWAGGER, swagger.isSelected());
            settings.set(SysConstant.FIELD_TABLE_PRE, field.getText());
            state.pluginSettingJson = settings.toString();
            pluginSettings.loadState(state);
            NotificationUtils.notifySuccess("配置保存成功", "提示", project);
        });
    }


    @Override
    protected void dispose() {
        editor.dispose();
        super.dispose();
    }

    @Override
    protected void init() {
        super.init();

    }

    public void createClass(String className, String code) {
        Project project = actionEvent.getProject();
        PsiFileFactory psiFileFactory = PsiFileFactory.getInstance(project);
        DataContext dataContext = actionEvent.getDataContext();
        PsiDirectory psiDirectory = (PsiDirectory) LangDataKeys.PSI_ELEMENT.getData(dataContext);
        String fileName = className + ".java";
        PsiFile newFile = psiFileFactory.createFileFromText(fileName, JavaFileType.INSTANCE, code);
        FileDocumentManager.getInstance().saveDocument(newFile.getViewProvider().getDocument());
        // 将新文件添加到选中的目录
        try {
            psiDirectory.add(newFile);
        } catch (IncorrectOperationException e) {
            showNotification(project, fileName, psiDirectory);
            throw e;
        }
    }

    public void showNotification(Project project, String fileName, PsiDirectory directory) {
        WaringDialog dialog = new WaringDialog(project, fileName, () -> {
            directory.findFile(fileName).delete();
            generateCode();
            super.doOKAction();
        });
        dialog.show();
    }

    @Override
    protected void doOKAction() {
        generateCode();
        super.doOKAction();
    }

    private void generateCode() {
        TableDTO tableInfo = DDLParser.parseDDLFields(editor.getEditor().getDocument().getText());

        // 如果是从项目视图中右键点击的进来的则创建新的类
        String place = actionEvent.getPlace();


        // 类里面进来的
        PsiFile psiFile = actionEvent.getData(CommonDataKeys.PSI_FILE);

        Project project = actionEvent.getProject();
        PsiElementFactory elementFactory = JavaPsiFacade.getElementFactory(project);

        Set<String> packageSet = tableInfo.getPackageSet();
        Set<String> classAnno = new HashSet<>();
        classAnnoAdd(classAnno, packageSet, tableInfo);
        String className = StrUtil.upperFirst(StrUtil.toCamelCase(tableInfo.getTableName().replace(field.getText(), "")));
        WriteCommandAction.runWriteCommandAction(project, () -> {
            PsiJavaFile psiJavaFile = (PsiJavaFile) psiFile;
            PsiClass psiClass = ObjectUtil.isNull(psiFile) ? elementFactory.createClass(className) : psiJavaFile.getClasses()[0];
            if (ObjectUtil.isNull(psiJavaFile)) {
                psiJavaFile = (PsiJavaFile) psiClass.getContainingFile();
            }
            // 添加类注释
            DocumentUtils.createDocumentAppendBefore(psiClass, tableInfo.getTableComment());
            // 导入包
            addImportPackage(packageSet, elementFactory, psiJavaFile);

            addClassAnno(psiClass, classAnno, elementFactory);
            // 字段属性
            generateCode(tableInfo.getColumnList(), elementFactory, psiClass);

            // 格式化代码
            CodeStyleManager codeStyleManager = CodeStyleManager.getInstance(project);
            PsiElement reformat = codeStyleManager.reformat(psiJavaFile);
            if (ActionPlaces.PROJECT_VIEW_POPUP.equals(place)) {
                createClass(className, reformat.getText());
            }
        });
        toEditorFile(className, project);
        NotificationUtils.notifySuccess("已为您打开", "生成成功", project);
    }

    /**
     * 打开当前生成的文件在编辑器
     *
     * @param className
     * @param project
     */
    private void toEditorFile(String className, Project project) {
        DataContext dataContext = actionEvent.getDataContext();
        PsiDirectory psiDirectory = (PsiDirectory) LangDataKeys.PSI_ELEMENT.getData(dataContext);
        VirtualFile virtualFile = psiDirectory.findFile(className + ".java").getVirtualFile();
        if (virtualFile != null) {
            virtualFile.refresh(false, false);
            // 在编辑器中打开新文件
            FileEditorManager.getInstance(project).openFile(virtualFile, true);
        }
    }


    /**
     * 添加类注解
     *
     * @param classAnno  类集合
     * @param packageSet 包集合
     * @param tableInfo  表信息
     */
    private void classAnnoAdd(Set<String> classAnno, Set<String> packageSet, TableDTO tableInfo) {
        if (mybatisPlusBox.isSelected()) {
            classAnno.add(SysConstant.ANNO_TABLE_NAME + "(\"" + tableInfo.getTableName() + "\")");
            packageSet.add(SysConstant.PACK_MYBATIS_PLUS_PACKAGE);

        }
        if (dataBox.isSelected()) {
            classAnno.add(SysConstant.ANNO_LOMBOK_DATA);
            packageSet.add(SysConstant.PACK_LOMBOK_PACKAGE);
        }
        if (swagger.isSelected()) {
            if (StrUtil.isNotBlank(tableInfo.getTableComment())) {
                classAnno.add(SysConstant.ANNO_API_MODEL + "(\"" + tableInfo.getTableComment() + "\")");
            } else {
                classAnno.add(SysConstant.ANNO_API_MODEL);
            }
            packageSet.add(SysConstant.PACK_SWAGGER_PACKAGE);
        }

    }

    /**
     * 类添加注解
     *
     * @param psiClass       目标类
     * @param annoList       注解列表
     * @param elementFactory 工厂
     */
    private static void addClassAnno(PsiClass psiClass, Collection<String> annoList, PsiElementFactory
            elementFactory) {
        List<String> exsitAnnoList = Arrays.stream(psiClass.getAnnotations()).map(PsiAnnotation::getText).toList();
        PsiModifierList modifierList = psiClass.getModifierList();

        for (String annoName : annoList) {
            if (exsitAnnoList.contains(annoName)) {
                continue;
            }
            PsiAnnotation annotation = elementFactory.createAnnotationFromText(annoName, null);
            modifierList.addBefore(annotation, modifierList.getFirstChild());
        }


    }

    /**
     * 导入字段类型包
     *
     * @param elementFactory
     * @param psiJavaFile
     */
    private static void addImportPackage(Set<String> packageSet, PsiElementFactory elementFactory, PsiJavaFile
            psiJavaFile) {
        if (CollUtil.isNotEmpty(packageSet)) {
            for (String packageName : packageSet) {
                PsiImportList importList = psiJavaFile.getImportList();

                for (PsiImportStatementBase allImportStatement : importList.getAllImportStatements()) {
                    if (allImportStatement.getText().contains(packageName)) {
                        return;
                    }
                }
                PsiImportStatement importStatement = elementFactory.createImportStatementOnDemand(packageName);
                importList.add(importStatement);
            }
        }
    }

    /**
     * 生成字段以及相关注解
     *
     * @param list
     * @param elementFactory
     * @param psiClass
     */
    public void generateCode(List<ColumnDTO> list, PsiElementFactory elementFactory, PsiClass psiClass) {
        List<String> fieldList = Arrays.stream(psiClass.getFields()).map(PsiField::getName).toList();
        for (ColumnDTO dto : list) {
            String fieldName = StrUtil.toCamelCase(dto.getFieldName());
            if (fieldList.contains(fieldName)) {
                continue;
            }
            PsiField field = elementFactory.createFieldFromText("private " + dto.getFieldType() + " " + fieldName + ";\n\n", null);
            if (mybatisPlusBox.isSelected()) {
                generateMybatisPlus(field, dto, elementFactory);
            }
            if (swagger.isSelected()) {
                generateSwagger(field, dto, elementFactory);
            }
            DocumentUtils.createDocumentAppendBefore(field, dto.getFieldComment());

            psiClass.add(field);
        }
    }

    /**
     * 生成mybatis-plus注解
     *
     * @param field          字段
     * @param dto            字段信息
     * @param elementFactory 工厂
     */

    public void generateMybatisPlus(PsiField field, ColumnDTO dto, PsiElementFactory elementFactory) {
        PsiAnnotation annotation;
        if (dto.isPrimaryKey()) {
            annotation = elementFactory.createAnnotationFromText(SysConstant.ANNO_TABLE_ID + "(\"" + dto.getFieldName() + "\")", null);
        } else {
            annotation = elementFactory.createAnnotationFromText(SysConstant.ANNO_TABLE_FIELD + "(\"" + dto.getFieldName() + "\")", null);
        }
        field.getModifierList().addBefore(annotation, field.getModifierList().getFirstChild());
    }

    /**
     * 生成swagger注解
     *
     * @param field          字段
     * @param dto            字段信息
     * @param elementFactory 工厂
     */
    public void generateSwagger(PsiField field, ColumnDTO dto, PsiElementFactory elementFactory) {
        PsiAnnotation annotation = elementFactory.createAnnotationFromText(SysConstant.ANNO_API_MODEL_PROPERTY + "(\"" + dto.getFieldComment() + "\")", null);
        field.getModifierList().addBefore(annotation, field.getModifierList().getFirstChild());
    }

}