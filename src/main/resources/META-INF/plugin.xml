<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>club.bigtian</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name><PERSON><PERSON>-Tools</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor email="<EMAIL>" url="https://www.yourcompany.com">bigtian yyds</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    Enter short description fo1r your plugin here.<br>
    <em>most HTML tags may be u1sed</em>
  ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.vcs</depends>
    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.lang</depends>
    <!--必须依赖Database Tool插件-->
    <depends>com.intellij.database</depends>

    <extensions defaultExtensionNs="com.intellij">
        <!--        <toolWindow id="流程模板" icon="/icons/flow.png" anchor="right"-->
        <!--                    factoryClass="club.bigtian.tool.WorkFLowToolWindowFactory"/>-->
        <!-- 项目级别设置 -->
        <projectService serviceImplementation="club.bigtian.zendao.settings.ZentaoSettings"/>
        <!-- 全局设置 -->
        <applicationService serviceImplementation="club.bigtian.zendao.settings.ZentaoGlobalSettings"/>
        <!-- 注册通知组 -->
        <notificationGroup id="Git Commit Helper" displayType="BALLOON" toolWindowId="Messages" />
        <!-- Add your extensions here -->
        <notificationGroup id="Zentao Notification Group" displayType="BALLOON" />
    </extensions>

    <actions>
        <group id="MyPlugin.SampleMenu" text="RZ" description="rzdata tools">
            <add-to-group group-id="MainMenu" anchor="last" />
            <action id="MyPlugin.FindMagicValues" class="club.bigtian.acttion.FindMagicValuesAction" text="Find Magic Values" description="Find magic values in all classes" />
        </group>
        <action id="bigtian" class="club.bigtian.acttion.DdlGenerateAction" text="DDL转实体"
                description="DDL生成实体属性" icon="/icons/database.png">
            <add-to-group group-id="GenerateGroup" anchor="first"/>
            <add-to-group group-id="ProjectViewPopupMenu" anchor="last"/>
        </action>
        <action id="documentFormatAction" class="club.bigtian.acttion.DocumentFormatAction" text="注释格式化"
                description="用于单行注释转多行注释">
            <add-to-group group-id="GenerateGroup" anchor="first"/>
        </action>
        <action id="WorkFlowAction_ID" class="club.bigtian.acttion.WorkFlowAction" text="流程代码生成"
                icon="/icons/flow.png"
                description="根据流程zip包生成通用代码">
            <add-to-group group-id="NewGroup" anchor="last"/>
        </action>
        <action id="ExcelToSqlAction_ID" class="club.bigtian.acttion.ExcelToSqlAction" text="Excel转SQL"
                icon="/icons/database.png"
                description="通过Excel数据生成对应表的SQL语句">
            <add-to-group group-id="DatabaseViewPopupMenu" anchor="first"/>
        </action>

        <action id="ExtractChineseAction" class="club.bigtian.acttion.ExtractChineseAction" text="ExtractChineseAction">
            <add-to-group group-id="BuildMenu" anchor="first"/>
        </action>

        <action id="GitCommit.TemplateButton" class="club.bigtian.zendao.action.GitCommitTemplateAction"
                text="选择提交模板"
                description="选择一个Git提交消息模板"
                icon="/icons/commit_icon.svg">
            <add-to-group group-id="Vcs.MessageActionGroup" anchor="last"/>
        </action>

        <!-- 添加禅道主菜单组 -->
        <group id="GitCommit.ZentaoGroup"
               text="禅道"
               description="禅道相关功能"
               icon="icons/zentao.svg"
               popup="true">
            <add-to-group group-id="ToolsMenu" anchor="last"/>

            <!-- 添加今日工作子菜单项 -->
            <action id="GitCommit.TodayWork"
                    class="club.bigtian.zendao.action.TodayWorkAction"
                    text="今日工作"
                    icon="icons/work.svg"
                    description="查看今日完成的工作内容"/>

            <!-- 添加禅道设置子菜单项 -->
            <action id="GitCommit.ZentaoSettings"
                    class="club.bigtian.zendao.action.ZentaoSettingsAction"
                    icon="/icons/settings.svg"
                    text="设置"
                    description="配置禅道的地址和账号"/>

            <!-- 添加导入任务子菜单项 -->
            <action id="GitCommit.ImportTask"
                    class="club.bigtian.zendao.action.ImportTaskAction"
                    text="导入任务"
                    icon="/icons/import.svg"
                    description="从禅道导入任务"/>
        </group>
    </actions>


</idea-plugin>