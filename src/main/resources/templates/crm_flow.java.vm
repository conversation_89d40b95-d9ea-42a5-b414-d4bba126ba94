package $package;
    #if($finalConstant==false)
    import ${finalConstantClass};
    #end
import java.util.Arrays;
import java.util.List;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.HashMap;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Function;
import java.util.Map;

import com.blueland.flow.abstracts.WorkFlowAbstract;
import cn.hutool.core.lang.Dict;
import com.blueland.flow.FlowDto;
import com.blueland.common.core.utils.UserUtils;
/**
 * $description
 * @time  $createTime
 * @since 1.0
 */
@Component
@Slf4j
public class ${className} extends WorkFlowAbstract {

    private Map<String, Function<FlowDto, Dict>> functionMap = new HashMap();

    #if($finalConstant)
        /**
        * 流程定义KEY
        */
        public static final String $processDefKeyFinal="${processDefKey}";
        #foreach($item in $nodeList)
            /**
             *  $item.activityDefName
             */
            private static final String $item.activityDefIdFinal ="$item.activityDefId";
        #end

        #foreach($item in $nodeList)
            #if($item.nextActivityDefId!='end')
                #if($item.type=='role')
                    /**
                     *  角色编码: $item.roleCode
                     */
                    private static final String ROLE_$item.roleCodeUpper = "$item.roleCode";
                #end
                #if($item.type=='dept')
                    /**
                     *  部门编码: $item.deptCode
                     */
                    private static final String DEPT_$item.deptCodeUpper = "$item.deptCode";
                    /**
                     *  角色编码: $item.roleCode
                     */
                    private static final String ROLE_$item.roleCodeUpper = "$item.roleCode";
                #end
                #if($item.type=='user')
                    /**
                     *  人员编码: $item.receive
                     */
                    private static final String USER_$item.receiveUpper = "$item.receive";
                #end
            #end
        #end
    #end

    @PostConstruct
    public void init () {
        #foreach($item in $nodeList)
            #if($item.nextActivityDefIdFinal)
                /**
                 * $item.activityDefName
                 *
                 */
                functionMap.put(#if($finalConstant==false)${finalConstantClass}.#end${item.activityDefIdFinal}, this::$item.activityDefIdCame);
            #end
        #end
    }
    #foreach($item in $nodeList)

        #if($item.nextActivityDefIdFinal)
            /**
             * $item.activityDefName
             * @param flowDto
             * @time 2023/5/20 09:42
             * @since 1.0
             */
    public Dict $item.activityDefIdCame (FlowDto flowDto) {
            #if($item.nextActivityDefId=='end')
                    flowDto.setNextActDefId(#if($finalConstant==false)${finalConstantClass}.#end$item.nextActivityDefIdFinal);
                over(flowDto);
                return null;
            #else
                    flowDto.setNextActDefId(#if($finalConstant==false)${finalConstantClass}.#end$item.nextActivityDefIdFinal);
                #if($item.type=='dept')
                    flowDto.setDept(#if($finalConstant==false)${finalConstantClass}.#end DEPT_$item.deptCodeUpper);
                    flowDto.setRoleCode(#if($finalConstant==false)${finalConstantClass}.#end ROLE_$item.roleCodeUpper);
                    #if($velocityCount==1)
                        return startWork(flowDto.buildDeptRoleTitle());
                    #else
                        return nextDeptRole(flowDto.buildDeptRoleTitle());
                    #end
                #elseif($item.type=='user')
                    #if(${item.receive}!='')
                        flowDto.setReceivedIds(Arrays.asList(#if($finalConstant==false)${finalConstantClass}.#end USER_${item.receiveUpper}.split(",")));
                    #end
                    //如果在插件中选择环节类型为人员且没有输入人员唯一标识，则默认要从前端传入人员唯一标识
                    #if($velocityCount==1)
                        return startWork(flowDto.buildReceivedsTitle());
                    #else
                        return nextAssistant(flowDto.buildReceivedsTitle());
                    #end
                #else
                    flowDto.setRoleCode(#if($finalConstant==false)${finalConstantClass}.#end ROLE_$item.roleCodeUpper);
                    #if($velocityCount==1)
                        return startWork(flowDto.buildRoleTitle());
                    #else
                        return nextRole(flowDto.buildRoleTitle());
                    #end
                #end
            #end
        }
        #end
    #end

    /**
     * 统一处理(入口)
     * <AUTHOR> flow generate
     * @since 1.0
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handler (FlowDto flowDto){
        //procDefKey
            flowDto.setProcDefKey(#if($finalConstant==false)${finalConstantClass}.#end$processDefKeyFinal);
        //如果审核不同意，则流程结束
        if (flowDto.getStatus().equals("N")) {
            over(flowDto);
            completeBacklog(flowDto, UserUtils.getStaffPostCode());
            return;
        }
        Function<FlowDto, Dict> function = functionMap.get(flowDto.getCurActDefId());
        Assert.notNull(function, "未找到对应的环节");
        //执行方法
        Dict workInfo = function.apply(flowDto);
        //不是发起阶段、处理自身的待办
        if (!#if($finalConstant==false)${finalConstantClass}.#end${nodeList.get(0).activityDefIdFinal}.
        equals(flowDto.getCurActDefId())){
            completeBacklog(flowDto, UserUtils.getStaffPostCode());
        }
        //最后一个环节返回都是 null，null就不需要发送待办
        if (ObjectUtil.isNotNull(workInfo)) {
            //添加待办
            backlogTitle(flowDto, workInfo);
        }
    }
}