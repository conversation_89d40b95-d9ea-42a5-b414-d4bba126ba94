package $package;
    #if($finalConstant==false)
    import ${finalConstantClass};
    #end
import java.util.Arrays;
import java.util.List;
import com.rzdata.workflow.core.interfaces.UserInterface;
import lombok.extern.slf4j.Slf4j;
import com.rzdata.workflow.core.abstracts.WorkFlowAbstract;
import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.function.Function;
import java.util.Map;
import com.rzdata.workflow.core.dto.BacklogDTO;
import com.rzdata.workflow.core.dto.FlowDTO;
import com.rzdata.workflow.core.dto.WorkInfo;

/**
 * $description
 * @time  $createTime
 * @since 1.0
 */
@Component
@Slf4j
public class ${className} extends WorkFlowAbstract{

private Map<String, Function<FlowDTO, WorkInfo>>functionMap=new HashMap();

#if($finalConstant)
/**
* 流程定义KEY
*/
public static final String $processDefKeyFinal="${processDefKey}";
#foreach($item in $nodeList)
/**
 *  $item.activityDefName
 */
private static final String $item.activityDefIdFinal ="$item.activityDefId";
#end

    #foreach($item in $nodeList)
        #if($item.nextActivityDefId!='end')
        #if($item.type=='role')
        /**
         *  角色编码: $item.roleCode
         */
        private static final String ROLE_$item.roleCodeUpper ="$item.roleCode";
        #end
        #if($item.type=='dept')
        /**
         *  部门编码: $item.deptCode
         */
        private static final String DEPT_$item.deptCodeUpper ="$item.deptCode";
        /**
         *  角色编码: $item.roleCode
         */
        private static final String ROLE_$item.roleCodeUpper ="$item.roleCode";
        #end
        #if($item.type=='user')
        /**
         *  人员编码: $item.receive
         */
        private static final String USER_$item.receiveUpper ="$item.receive";
        #end
        #end
    #end
#end
@Autowired
private UserInterface userInterface;

@PostConstruct
public void init(){
    #foreach($item in $nodeList)
        #if($item.nextActivityDefIdFinal)
                /**
                 * $item.activityDefName
                 *
                 */
                functionMap.put(#if($finalConstant==false)${finalConstantClass}.#end${item.activityDefIdFinal},this::$item.activityDefIdCame);
        #end
    #end
        }
#foreach($item in $nodeList)
    #if($item.nextActivityDefIdFinal)
/**
 * $item.activityDefName
 * @param flowDTO
 * @time 2023/5/20 09:42
 * @since 1.0
 */
public WorkInfo $item.activityDefIdCame (FlowDTO flowDTO){
    #if($item.nextActivityDefId=='end')
            flowDTO.setNextActDefId(#if($finalConstant==false)${finalConstantClass}.#end$item.nextActivityDefIdFinal);
            over(flowDTO);
            return null;
    #else
            flowDTO.setNextActDefId(#if($finalConstant==false)${finalConstantClass}.#end$item.nextActivityDefIdFinal);
        #if($item.type=='dept')
                flowDTO.setDept(#if($finalConstant==false)${finalConstantClass}.#end DEPT_$item.deptCodeUpper);
                flowDTO.setRoleCode(#if($finalConstant==false)${finalConstantClass}.#end ROLE_$item.roleCodeUpper);
            #if($velocityCount==1)
                    return startWork(flowDTO);
            #else
                    return nextDeptRole(flowDTO);
            #end
        #elseif($item.type=='user')
            #if(${item.receive}!='')
                    flowDTO.setReceivedIds(Arrays.asList(#if($finalConstant==false)${finalConstantClass}.#end USER_${item.receiveUpper}.split("," )));
            #end
                //如果在插件中选择环节类型为人员且没有输入人员唯一标识，则默认要从前端传入人员唯一标识
            #if($velocityCount==1)
                    return startWork(flowDTO);
            #else
                return nextAssistant(flowDTO);
            #end
        #else
                flowDTO.setRoleCode(#if($finalConstant==false)${finalConstantClass}.#end ROLE_$item.roleCodeUpper);
            #if($velocityCount==1)
                    return startWork(flowDTO);
            #else
                return nextRole(flowDTO);
            #end
        #end
    #end
        }
#end
#end

/**
 * 统一处理(入口)
 * <AUTHOR> flow generate
 * @since 1.0
 */
@Override
@Transactional(rollbackFor = Exception.class)
public void handler(FlowDTO flowDto){
        //procDefKey
        flowDto.setProcDefKey(#if($finalConstant==false)${finalConstantClass}.#end$processDefKeyFinal);
        //如果审核不同意，则流程结束
        if(flowDto.getStatus().equals("N")){
        over(flowDto);
        }
        Function<FlowDTO, WorkInfo> function=functionMap.get(flowDto.getCurActDefId());
        if(function==null){
        throw new RuntimeException("未找到对应的环节");
        }
        //执行方法
        WorkInfo workInfo=function.apply(flowDto);
        //不是发起阶段、处理自身的待办
        if(!#if($finalConstant==false)${finalConstantClass}.#end${nodeList.get(0).activityDefIdFinal}.equals(flowDto.getCurActDefId())){
        completeBacklog(flowDto);
        }
        //最后一个环节返回都是 null，null就不需要发送待办
        if(workInfo==null){
        return;
        }
        //组装待办数据
        BacklogDTO backlogDto=BacklogDTO.builder()
        .sendid(flowDto.getApplyUserId())
        //根据自身业务修改，标题默认 （业务编码【申请人】）
        .title(String.format("%s【%s】",flowDto.getBusiKey(),flowDto.getApplyName()))
        //根据自身业务修改
        .type(#if($finalConstant==false)${finalConstantClass}.#end$processDefKeyFinal)
        .busiParams(JSONObject.toJSONString(flowDto))
        .build();
        flowDto.setBacklog(backlogDto);
        backlog(flowDto,workInfo.getReceivaedList());
        }
        }
